import { ref } from 'vue'

export interface Task {
  id: string
  title: string
  status: string
  statusText: string
  submitTime: string
  executeTime: string
  purpose: string
  location: string
  image: string
  markerCount?: number
  photoCount?: number
}

export function useTaskManagement() {
  const taskList = ref<Task[]>([])
  const currentTask = ref<Task | null>(null)
  const hoveredTask = ref<Task | null>(null)
  const showDetail = ref(false)
  const showCompleteNotification = ref(false)
  const completeStatus = ref('')

  const statusMap = {
    pending: '待执行',
    inProgress: '执行中',
    completed: '已完成',
    expired: '已过期'
  }

  const handleTaskClick = (task: Task) => {
    currentTask.value = task
    showDetail.value = true
  }

  const handleTaskHover = (task: Task | null) => {
    hoveredTask.value = task
  }

  const goBack = () => {
    showDetail.value = false
    currentTask.value = null
  }

  return {
    taskList,
    currentTask,
    hoveredTask,
    showDetail,
    showCompleteNotification,
    completeStatus,
    statusMap,
    handleTaskClick,
    handleTaskHover,
    goBack
  }
} 