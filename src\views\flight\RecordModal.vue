<template>
  <BasicModal
    v-bind="$attrs"
    @register="register"
    :width="1100"
    :height="540"
    :canFullscreen="true"
    :showOkBtn="false"
    :showCancelBtn="false"
    :showHeader="false"
    :headerStyle="{ display: 'none' }"
    :bodyStyle="{ padding: 0 }"
    :closable="false"
    :maskClosable="false"
    :modalStyle="{ padding: 0, border: 'none', top: '-50px', position: 'absolute' }"
    :wrapClassName="'custom-modal'"
    :footer="null"
  >
    <div class="record-modal">
      <!-- 右上角关闭按钮 -->
      <div class="close-btn" @click="closeModalAndEmit">
        <CloseOutlined />
      </div>
      
      <!-- 左侧地图区域 -->
      <div class="map-section">
        <div class="map-container">
          <div id="mars2dContainer" class="record-map-view"></div>
          
          <!-- 飞行信息覆盖在地图上 -->
          <div class="flight-info">
            <div class="info-item">
              <div class="value">{{ flightDuration }}</div>
              <div class="label">飞行时长</div>
            </div>
            <div class="info-item">
              <div class="value">{{ flightDistance }}</div>
              <div class="label">飞行距离(km)</div>
            </div>
            <div class="info-item">
              <div class="value">{{ record?.photoNums || 0 }}</div>
              <div class="label">拍摄照片</div>
            </div>

          </div>
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <div class="content-section">
        <div class="record-header">
          <div class="record-title">{{ record?.recordName || '未命名记录' }}</div>
          <div class="record-info">
            <div class="info-row">
              <span class="label">起飞时间：</span>
              <span class="value">{{ takeoffTime }}</span>
              <span class="separator">|</span>
              <span class="label">创建人：</span>
              <span class="value">{{ creator }}</span>
            </div>
          </div>
        </div>

        <div class="filter-section">
          <div class="filter-buttons">
            <a-button 
              v-for="(btn, index) in filterButtons" 
              :key="index"
              :type="currentFilter === btn.key ? 'primary' : 'default'"
              @click="handleFilterChange(btn.key)"
            >
              {{ btn.label }}({{ btn.count }})
            </a-button>
          </div>
          <div class="filter-options">
            <a-checkbox v-model:checked="showIssues">
              问题数({{ record?.issuePhotoNums || 0 }})
            </a-checkbox>
            <a-switch
              v-model:checked="showWatermark"
              checked-children="水印"
              un-checked-children="水印"
            />
          </div>
        </div>

        <div class="content-display">
          <!-- 加载状态 -->
          <div v-if="isLoadingMedia" class="loading-container">
            <a-spin size="large" tip="正在加载媒体文件...">
              <div class="loading-content">
                <div class="loading-icon">⏳</div>
                <div class="loading-text">正在从MinIO存储获取图片和视频</div>
              </div>
            </a-spin>
          </div>
          
          <!-- 媒体网格显示 -->
          <div v-else-if="!isLoadingMedia" class="media-grid" :key="renderKey">

            <!-- 媒体项列表 -->
            <div 
              v-for="item in currentMediaList" 
              :key="item.id"
              class="media-item"
              @mouseenter="handleMediaHover(item.id, true)"
              @mouseleave="handleMediaHover(item.id, false)"
              @click="handleMediaClick(item)"
            >
              <div class="media-content">
                <img 
                  v-if="item.type === 'photo'" 
                  :src="item.url" 
                  :alt="item.name"
                  class="photo-image"
                  @error="handleImageError($event, item)"
                  @load="handleImageLoad($event, item)"
                  crossorigin="anonymous"
                  decoding="async"
                  :style="{ display: 'block', visibility: 'visible' }"
                />
                <video
                  v-else-if="item.type === 'video' || item.type === 'live'"
                  :src="item.url"
                  controls
                  preload="none"
                  class="video-player"
                  @error="handleVideoError($event, item)"
                  @loadedmetadata="handleVideoLoad($event, item)"
                ></video>
                
                <div class="media-info">
                  <span class="time">{{ item.time }}</span>
                  <a-button 
                    type="link" 
                    class="favorite-btn"
                    @click.stop="handleFavorite(item)"
                  >
                    <StarOutlined :class="{ 'is-favorite': item.isFavorite }" />
                  </a-button>
                </div>
                
                <div class="hover-actions" v-show="item.isHovered">
                  <a-button 
                    type="primary" 
                    class="download-btn"
                    @click.stop="handleDownload(item)"
                  >
                    <DownloadOutlined />
                  </a-button>
                </div>
                
                <div class="issue-icon" v-if="item.isIssue">
                  <img src="/src/assets/images/icon/warn.png" alt="警告" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详情弹窗 -->
    <a-modal
      v-model:open="detailVisible"
      :footer="null"
      :closable="false"
      :maskClosable="true"
      :width="900"
      :bodyStyle="{ padding: 0, height: '500px' }"
      @cancel="closeDetail"
    >
      <div class="detail-container" v-if="currentDetail" @wheel="handleWheel">
        <div class="detail-close" @click="closeDetail">
          <CloseOutlined />
        </div>
        
        <div class="detail-content" :class="{ 'zoomed': isZoomed }">
          <img 
            v-if="currentDetail.type === 'photo'" 
            :src="currentDetail.url" 
            class="detail-image" 
            @click="handleImageClick"
            :style="{ transform: `scale(${zoomLevel})` }"
          />
          <video 
            v-else 
            :src="currentDetail.url" 
            controls 
            class="detail-video"
          ></video>
        </div>
      </div>
    </a-modal>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted, nextTick, onUnmounted } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { fetchMinioConfig, getImageUrl } from '/@/utils/minio';
import { 
  DownloadOutlined as DownloadIcon, 
  StarOutlined as StarIcon, 
  CloseOutlined as CloseIcon
} from '@ant-design/icons-vue';
// 使用Mars框架的标准方式
import useLifecycle from "@mars/common/uses/use-lifecycle"
import * as mapWork from "/@/marsgis/widgets/flight/record-detail/map"
import * as mars2d from "mars2d"

// 定义组件名称
defineOptions({
  name: 'RecordModal'
});

interface Record {
  id: string;
  recordName: string;
  type: string;
  airport: string;
  creator: string;
  flightTime: string;
  photoNums: number;
  issuePhotoNums: number;
  photoStatus: string;
  takeoffTime?: string;
  createTime?: string;
  createBy?: string;
  flightPath?: any;
  taskId?: string;
  task_id?: string;
  flightTaskId?: string;
  flight_task_id?: string;
  taskID?: string;
  TASK_ID?: string;
  [key: string]: any;
}

interface MediaItem {
  id: string;
  type: 'photo' | 'video' | 'live';
  url: string;
  name: string;
  time: string;
  isFavorite: boolean;
  isHovered: boolean;
  isIssue: boolean;
  raw?: any; // 保存原始数据供后续使用
}

const props = defineProps<{
  record: Record | null;
}>();

const emit = defineEmits(['close']);

// 地图相关变量
const map = ref<mars2d.Map | null>(null);

// 使用Mars框架的标准方式
useLifecycle(mapWork)

const [register, { closeModal }] = useModalInner((data) => {

  
  if (data && data.record) {
    // 重置状态但保持响应式连接
    resetComponentState();
    
    // 立即设置加载状态
    isLoadingMedia.value = true;
    
    // 等待模态框DOM完全渲染后再初始化数据
    nextTick(() => {
      setTimeout(async () => {
        try {
          // 先并行加载数据
          await Promise.all([
            initRecordData(),
            new Promise(resolve => {
              setTimeout(() => {
                initRecordMap();
                resolve(true);
              }, 200);
            })
          ]);
          
          // 确保响应式更新
          await nextTick();
          
          // 强制触发视图更新
          renderKey.value = Date.now();
          
        } catch (error) {
          console.error('初始化失败:', error);
          isLoadingMedia.value = false;
        }
      }, 300);
    });
  }
});

// 重置组件状态的函数
const resetComponentState = () => {
  isMediaInitialized.value = false;
  currentRecordId.value = null;
  isLoadingMedia.value = false;
  flightTaskData.value = null;
  currentFilter.value = 'all';
  showIssues.value = false;
  
  // 重置详情弹窗状态
  detailVisible.value = false;
  currentDetail.value = null;
  isZoomed.value = false;
  zoomLevel.value = 1;
};

// 配置常量
const CONFIG = {
  MAP: {
    DELAYS: { MAP_INIT: 800, VIEW_SETTING: 1000 },
    STYLES: {
      LINE: { color: "#005FFF", width: 3, opacity: 0.8 },
      POINTS: {
        START_COLOR: "#00ff00", END_COLOR: "#ff0000", MIDDLE_COLOR: "#ff6600",
        START_SIZE: 10, END_SIZE: 10, MIDDLE_SIZE: 8
      }
    },
    MARKERS: { DRONE: "/img/marker/drone.png" }
  },
  API: {
    REQUEST_TIMEOUT: 5000,
    TASK_ID_FIELDS: ['taskId', 'task_id', 'flightTaskId', 'flight_task_id', 'taskID', 'TASK_ID'],
    FLIGHT_PATH_FIELDS: ['flightPath', 'flight_path']
  },
  MEDIA: { PAGE_SIZE: 1000, VIDEO_PAGE_SIZE: 200 }
};

// 优化的地图初始化函数
const initRecordMap = async () => {
  if (!props.record?.id) {
    return;
  }

  try {
    const mapContainer = document.getElementById('mars2dContainer');
    if (!mapContainer) {
      return;
    }

    // 清理之前的地图实例
    if (mapWork.onUnmounted) {
      mapWork.onUnmounted();
    }

    await new Promise(resolve => setTimeout(resolve, 100));

    // 创建地图实例
    map.value = mapWork.initMap('mars2dContainer');
    
    await new Promise(resolve => setTimeout(resolve, CONFIG.MAP.DELAYS.MAP_INIT));

    // 获取并绘制飞行轨迹
    const flightPath = await getFlightPathData();
    
    if (flightPath && flightPath.coordinates && flightPath.coordinates.length > 0) {
      drawFlightPath(flightPath);
    } else {
      // 没有轨迹数据时使用默认坐标
      const defaultFlightPath = {
        type: "LineString",
        coordinates: [[25.0566, 102.8905], [25.0566, 102.8902]]
      };
      drawFlightPath(defaultFlightPath);
    }
    
  } catch (error) {
    console.error('地图初始化失败:', error);
    const mapContainer = document.getElementById('mars2dContainer');
    if (mapContainer) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      showMapError(mapContainer, '地图初始化失败: ' + errorMessage);
    }
  }
};

// 显示地图错误信息
const showMapError = (container: HTMLElement, message: string) => {
  container.innerHTML = `
    <div style="display: flex; align-items: center; justify-content: center; height: 100%; flex-direction: column; color: #999;">
      <div style="font-size: 32px; margin-bottom: 8px;">🗺️</div>
      <div style="font-size: 14px;">${message}</div>
      <div style="font-size: 12px; margin-top: 4px;">请稍后重试</div>
    </div>
  `;
};

// 绘制飞行轨迹函数
const drawFlightPath = (flightPath: any) => {
  if (!map.value) {
    return;
  }
  
  try {
    let coordinates: number[][] = [];
    
    // 处理坐标数据
    if (flightPath && flightPath.coordinates && Array.isArray(flightPath.coordinates)) {
      coordinates = flightPath.coordinates;
      
      // 坐标格式检测和转换
      if (coordinates.length > 0) {
        const firstCoord = coordinates[0];
        // 检测是否为 [纬度,经度] 格式
        if (firstCoord[0] >= -90 && firstCoord[0] <= 90 && 
            firstCoord[1] >= -180 && firstCoord[1] <= 180 &&
            Math.abs(firstCoord[0]) < Math.abs(firstCoord[1])) {
          // 数据格式已经是 [纬度, 经度]，直接使用
        } else {
          // 转换为 [纬度, 经度] 格式
          coordinates = coordinates.map((coord: number[]) => [coord[1], coord[0]]);
        }
      }
    } else {
      // 使用默认坐标
      coordinates = [[25.0566, 102.8905], [25.0566, 102.8902]];
    }

    if (coordinates.length === 0) {
      return;
    }

    // 清除之前的图层
    if (map.value.graphicLayer) {
      map.value.graphicLayer.clear();
    }

    // 绘制飞行轨迹线
    const line = new mars2d.graphic.Polyline({
      latlngs: coordinates,
      style: CONFIG.MAP.STYLES.LINE
    });
    
    if (map.value.graphicLayer) {
      map.value.graphicLayer.addGraphic(line);
    }

    // 绘制航线点标记
    if (coordinates.length > 0) {
      drawFlightPoints(coordinates);
      setMapView(coordinates[0]);
    }
  } catch (error) {
    console.error('绘制飞行轨迹失败:', error);
  }
};

// 绘制航线点的独立函数
const drawFlightPoints = (coordinates: number[][]) => {
  if (!map.value) return;
  
  // 绘制所有航线点
  for (let i = 0; i < coordinates.length; i++) {
    const coord = coordinates[i];
    const isStartPoint = i === 0;
    const isEndPoint = i === coordinates.length - 1;
    
    // 创建点标记
    const pointGraphic = new mars2d.graphic.Point({
      latlng: coord,
      style: {
        color: isStartPoint ? CONFIG.MAP.STYLES.POINTS.START_COLOR : 
               (isEndPoint ? CONFIG.MAP.STYLES.POINTS.END_COLOR : CONFIG.MAP.STYLES.POINTS.MIDDLE_COLOR),
        pixelSize: isStartPoint || isEndPoint ? 
                   (isStartPoint ? CONFIG.MAP.STYLES.POINTS.START_SIZE : CONFIG.MAP.STYLES.POINTS.END_SIZE) : 
                   CONFIG.MAP.STYLES.POINTS.MIDDLE_SIZE,
        outline: true,
        outlineColor: "#ffffff",
        outlineWidth: 2
      },
      popup: isStartPoint ? `起飞点<br/>纬度: ${coord[0]}<br/>经度: ${coord[1]}` : 
             (isEndPoint ? `降落点<br/>纬度: ${coord[0]}<br/>经度: ${coord[1]}` : 
              `航点 ${i}<br/>纬度: ${coord[0]}<br/>经度: ${coord[1]}`)
    });
    
    if (map.value.graphicLayer) {
      map.value.graphicLayer.addGraphic(pointGraphic);
    }
  }
  
  // 额外绘制起飞点的无人机图标
  const startMarker = new mars2d.graphic.Marker({
    latlng: coordinates[0],
    style: {
      image: CONFIG.MAP.MARKERS.DRONE,
      width: 48,
      height: 48,
    },
    popup: `起飞点<br/>纬度: ${coordinates[0][0]}<br/>经度: ${coordinates[0][1]}`
  });
  
  if (map.value.graphicLayer) {
    map.value.graphicLayer.addGraphic(startMarker);
  }
};

// 设置地图视图的独立函数
const setMapView = (startCoordinate: number[]) => {
  setTimeout(() => {
    if (map.value && map.value.flyTo) {
      map.value.flyTo({
        lat: startCoordinate[0],
        lng: startCoordinate[1]
      }, 15, { duration: 2 });
    }
  }, CONFIG.MAP.DELAYS.VIEW_SETTING);
};

// 获取全局配置
const globSetting = useGlobSetting();
const { viewUrl } = globSetting;

// 计算问题图片数量
const issuePhotoCount = computed(() => {
  return mediaList.value.filter(item => item.type === 'photo' && item.isIssue).length;
});

// 过滤按钮数据
const filterButtons = computed(() => [
  { 
    key: 'all', 
    label: '全部', 
    count: allMediaList.value.length
  },
  {
    key: 'photo',
    label: '照片',
    count: photoList.value.length
  },
  { 
    key: 'video', 
    label: '视频', 
    count: videoList.value.length 
  },
  // { 
  //   key: 'live', 
  //   label: '直播回放', 
  //   count: liveList.value.length 
  // },
  { 
    key: 'favorite', 
    label: '收藏', 
    count: favoriteList.value.length 
  },
]);

// 当前选中的过滤类型
const currentFilter = ref('all');

// 是否显示问题图片
const showIssues = ref(false);

// 是否显示水印
const showWatermark = ref(false);

// 强制重渲染的key
const renderKey = ref(0);

// 媒体加载状态
const isLoadingMedia = ref(false);
// 移除重复加载的严格限制，改为基于模态框状态的控制
const isMediaInitialized = ref(false);
const currentRecordId = ref<string | null>(null);

// 根据类型过滤媒体列表
const photoList = computed(() => {
  return mediaList.value.filter(item => item.type === 'photo');
});

// 问题照片列表
const issuePhotoList = computed(() => {
  return mediaList.value.filter(item => item.type === 'photo' && item.isIssue);
});

// 根据"问题数"复选框状态过滤照片列表
const filteredPhotoList = computed(() => {
  // 当勾选问题数时，photoList已经只包含问题图片了
  return photoList.value;
});

// 全部媒体列表（根据问题数复选框过滤）
const allMediaList = computed(() => {
  // 当勾选问题数时，mediaList已经只包含问题图片了
  // 所以直接返回mediaList即可
  return mediaList.value;
});

const videoList = computed(() => mediaList.value.filter(item => item.type === 'video'));
const liveList = computed(() => mediaList.value.filter(item => item.type === 'live'));

// 媒体数据列表
const mediaList = ref<MediaItem[]>([]);

// 添加详情状态
const detailVisible = ref(false);
const currentDetail = ref<MediaItem | null>(null);

// 添加缩放状态
const isZoomed = ref(false);
const zoomLevel = ref(1);

// 添加收藏列表计算属性
const favoriteList = computed(() => {
  return mediaList.value.filter(item => item.isFavorite);
});

// 统一的当前媒体列表计算属性
const currentMediaList = computed(() => {
  switch (currentFilter.value) {
    case 'photo':
      return filteredPhotoList.value;
    case 'video':
      return videoList.value;
    case 'live':
      return liveList.value;
    case 'favorite':
      return favoriteList.value;
    case 'all':
    default:
      return allMediaList.value;
  }
});

// 优化的起飞时间计算属性
const takeoffTime = computed(() => {
  return props.record?.takeoffTime || props.record?.createTime || props.record?.flightTime || '未知';
});

// 优化的创建人计算属性
const creator = computed(() => {
  return props.record?.createBy || props.record?.creator || '未知';
});

// 添加提取组织名称的函数
const extractOrgName = (recordName: string): string => {
  // 从记录名称中提取组织名称
  // 例如：从"全覆盖】滇中产业发展集团机场2-3000mB17"提取"滇中产业发展集团"
  const match = recordName.match(/】(.+?)(?:机场|$)/);
  return match ? match[1] : '';
};

// 飞行任务详情数据
const flightTaskData = ref<any>(null);

// 飞行时长计算 - 基于flight_tasks表的takeoff_time和landing_time
const flightDuration = computed(() => {
  // 计算时长的辅助函数
  const calculateDuration = (startTime: string | Date, endTime: string | Date): string => {
    const start = new Date(startTime);
    const end = new Date(endTime);
    
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return '';
    }
    
    const duration = Math.abs(end.getTime() - start.getTime());
    const hours = Math.floor(duration / (1000 * 60 * 60));
    const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((duration % (1000 * 60)) / 1000);
    return `${hours}小时${minutes}分${seconds}秒`;
  };
  
  // 优先使用flight_tasks表的数据（支持多种字段格式）
  const taskData = flightTaskData.value;
  if (taskData) {
    const takeoffFields = ['takeoff_time', 'takeoffTime'];
    const landingFields = ['landing_time', 'landingTime'];
    
    let takeoffTime = null;
    let landingTime = null;
    
    // 查找起飞时间
    for (const field of takeoffFields) {
      if (taskData[field]) {
        takeoffTime = taskData[field];
        break;
      }
    }
    
    // 查找降落时间
    for (const field of landingFields) {
      if (taskData[field]) {
        landingTime = taskData[field];
        break;
      }
    }
    
    if (takeoffTime && landingTime) {
      const result = calculateDuration(takeoffTime, landingTime);
      if (result) return result;
    }
  }
  
  // 备用方案：使用record中的数据
  // if (props.record) {
  //   const takeoffTime = props.record.takeoffTime || props.record.takeoff_time;
  //   const landingTime = props.record.createTime || props.record.create_time || props.record.landing_time;
    
  //   if (takeoffTime && landingTime) {
  //     const result = calculateDuration(takeoffTime, landingTime);
  //     if (result) return result;
  //   }
  // }
  
  return '-';
});

// 飞行距离计算 - 基于flight_records表的flight_distance字段
const flightDistance = computed(() => {
  // 优先使用record中的flight_distance字段（支持多种字段名）
  const distanceFields = ['flight_distance', 'flightDistance', 'distance'];
  
  for (const field of distanceFields) {
    if (props.record?.[field] && props.record[field] !== '0' && props.record[field] !== 0) {
      //飞行距离单位为m，转换为km
      const distance = parseFloat(props.record[field]) / 1000;
      if (!isNaN(distance) && distance > 0) {
        return distance.toFixed(4);
      }
    }
  }
  
  // 备用方案：从flight_tasks数据中获取
  if (flightTaskData.value?.distance && flightTaskData.value.distance !== '0') {
    const distance = parseFloat(flightTaskData.value.distance);
    if (!isNaN(distance) && distance > 0) {
      return distance.toFixed(1);
    }
  }
  
  return '未知';
});

// 获取飞行任务详情数据
const fetchFlightTaskData = async () => {
  const taskId = getValidTaskId();
  if (!taskId) {
    console.warn('无法获取taskId，跳过飞行任务详情查询');
    return;
  }

  try {
    const response = await defHttp.get({
      url: '/flight/flightTasks/queryById',
      params: { id: taskId },
      timeout: CONFIG.API.REQUEST_TIMEOUT
    });

    if (response?.result || response) {
      flightTaskData.value = response.result || response;
    }
  } catch (error) {
    console.warn('获取飞行任务详情失败:', error);
    flightTaskData.value = null;
  }
};

// 优化的初始化媒体数据函数
const initMediaList = async () => {
  if (!props.record) {
    console.warn('记录数据不存在，无法初始化媒体列表');
    return;
  }
  
  const recordId = props.record.id;
  const taskId = getValidTaskId();
  
  // 如果是同一个记录且已经初始化过，跳过加载
  if (isMediaInitialized.value && currentRecordId.value === recordId) {
    return;
  }
  
  console.log('开始初始化媒体列表，记录ID:', recordId, 'taskId:', taskId);
  
  // 更新当前记录ID（加载状态已在模态框初始化时设置）
  currentRecordId.value = recordId;
  
  // 确保MinIO配置已加载
  try {
    await fetchMinioConfig();
  } catch (error) {
    console.error('MinIO配置加载失败:', error);
  }
  
  const list: MediaItem[] = [];
  
  try {
    // 1. 获取巡检图片 - 支持多种taskId字段格式
    
    if (taskId) {
      // 获取巡检图片元数据，确保包含标注状态字段
      const photoResponse = await defHttp.get({
        url: '/flight/flightInspectionImages/list',
        params: {
          pageNo: 1,
          pageSize: CONFIG.MEDIA.PAGE_SIZE,
          taskId: taskId
        }
      });
      
      // 处理照片数据
      const photoRecords = extractRecordsFromResponse(photoResponse);
      
      // 批量处理照片，提高性能
      const batchSize = 10;
      for (let i = 0; i < photoRecords.length; i += batchSize) {
        const batch = photoRecords.slice(i, i + batchSize);
        const batchItems = await Promise.all(
          batch.map(async (photo: any, index: number) => {
            try {
              return await createPhotoMediaItem(photo, i + index);
            } catch (error) {
              console.error(`处理照片${photo.id}失败:`, error);
              return null;
            }
          })
        );
        
        // 只添加成功处理的项目
        batchItems.forEach(item => {
          if (item) list.push(item);
        });
      }
      
      // 2. 获取巡检视频元数据
      let videoItemsFromAPI: MediaItem[] = [];
      try {
        const videoResponse = await defHttp.get({
          url: '/flight/inspectionVideosEntity/list',
          params: {
            pageNo: 1,
            pageSize: CONFIG.MEDIA.VIDEO_PAGE_SIZE,
            taskId: taskId
          }
        });

        // 处理视频数据
        const videoRecords = extractRecordsFromResponse(videoResponse);

        // 使用Promise.all处理所有视频
        const videoItems = await Promise.all(
          videoRecords.map(async (video: any, index: number) => {
            return await createVideoMediaItem(video, index);
          })
        );

        // 只添加成功处理的项目
        videoItems.forEach(item => {
          if (item) {
            list.push(item);
            videoItemsFromAPI.push(item);
          }
        });

        console.log(`从API获取到 ${videoItemsFromAPI.length} 个视频`);
      } catch (videoError) {
        console.warn('获取视频数据API调用失败:', videoError);
      }

      // 如果从API获取的视频数量为0，尝试从图片数据推导视频
      if (videoItemsFromAPI.length === 0) {
        try {
          console.log('API未返回视频数据，开始从图片数据推导视频...');

          // 先尝试直接使用已知的视频路径
          const knownVideoPaths = [
            `djicloudapi/${taskId}/DJI_20250616100939_0001_S.mp4`,
            `${taskId}/DJI_20250616100939_0001_S.mp4`
          ];

          for (const videoPath of knownVideoPaths) {
            try {
              console.log(`尝试已知视频路径: ${videoPath}`);
              const videoUrl = await getImageUrl(videoPath);
              if (videoUrl) {
                const videoItem: MediaItem = {
                  id: `known-video-${Date.now()}`,
                  type: 'video',
                  url: videoUrl,
                  name: 'DJI_20250616100939_0001_S.mp4',
                  time: new Date().toISOString(),
                  isFavorite: false,
                  isHovered: false,
                  isIssue: false,
                  raw: {
                    derivedFromKnownPath: true,
                    videoPath: videoPath
                  }
                };
                list.push(videoItem);
                console.log(`成功添加已知视频: ${videoPath} -> ${videoUrl}`);
                break; // 找到一个就够了
              }
            } catch (error) {
              console.warn(`已知视频路径无效: ${videoPath}`, error);
            }
          }

          // 如果已知路径都不行，再尝试从图片数据推导
          if (list.filter(item => item.type === 'video').length === 0) {
            const derivedVideoItems = await Promise.all(
              photoRecords.slice(0, 3).map(async (photo: any, index: number) => {
                return await createVideoFromImageData(photo, index, taskId);
              })
            );

            // 添加成功推导的视频项目
            const validDerivedVideos = derivedVideoItems.filter(item => item);
            validDerivedVideos.forEach(item => {
              if (item) list.push(item);
            });

            console.log(`从图片数据推导出 ${validDerivedVideos.length} 个视频`);
          }
        } catch (derivationError) {
          console.warn('从图片数据推导视频失败:', derivationError);
        }
      }
    } else {
      console.warn('未找到有效的taskId，尝试使用记录ID作为备选');
      
      // 备选方案：直接使用记录ID
      try {
        const photoResponse = await defHttp.get({
          url: '/flight/flightInspectionImages/list',
          params: {
            pageNo: 1,
            pageSize: CONFIG.MEDIA.PAGE_SIZE,
            taskId: props.record.id
          }
        });
        
        const photoRecords = extractRecordsFromResponse(photoResponse);
        for (const photo of photoRecords) {
          const photoItem = await createPhotoMediaItem(photo, list.length);
          if (photoItem) {
            list.push(photoItem);
          }
        }
      } catch (fallbackError) {
        console.error('备选方案也失败:', fallbackError);
      }
    }
    
  } catch (error) {
    console.error('获取媒体数据失败:', error);
  } finally {
    // 确保在任何情况下都结束加载状态
    isLoadingMedia.value = false;
  }
  
  // 按时间排序，最新的在前面
  list.sort((a, b) => {
    if (!a.time || !b.time) return 0;
    return new Date(b.time).getTime() - new Date(a.time).getTime();
  });

  // 统计媒体类型
  const photoCount = list.filter(item => item.type === 'photo').length;
  const videoCount = list.filter(item => item.type === 'video').length;
  console.log(`媒体列表初始化完成: 照片 ${photoCount} 张, 视频 ${videoCount} 个, 总计 ${list.length} 项`);

  // 使用Vue.set确保响应式更新
  mediaList.value = [...list]; // 创建新数组确保响应式

  // 标记为已初始化
  isMediaInitialized.value = true;

  // 结束加载状态
  isLoadingMedia.value = false;
};

// 获取有效的taskId - 支持多种字段格式
const getValidTaskId = () => {
  if (!props.record) return null;
  
  for (const field of CONFIG.API.TASK_ID_FIELDS) {
    if (props.record[field]) {
      return props.record[field];
    }
  }
  return null;
};

// 从响应中提取记录数据的工具函数
const extractRecordsFromResponse = (response: any): any[] => {
  if (!response) return [];
  
  if (response.records) {
    return response.records;
  } else if (response.result && response.result.records) {
    return response.result.records;
  } else if (Array.isArray(response)) {
    return response;
  }
  
  return [];
};

// 创建已标注照片媒体项的工具函数 - 从annotated文件夹获取图片
const createAnnotatedPhotoMediaItem = async (photo: any, index: number, taskId: string): Promise<MediaItem | null> => {
  try {
    const photoId = photo.id || `annotated-photo-${Date.now()}-${index}`;

    // 获取原始图片名称
    const originalImageName = photo.imageName || photo.image_name || '';
    if (!originalImageName) {
      console.warn(`照片${photoId}缺少图片名称`);
      return null;
    }

    // 构造annotated文件夹中的图片路径
    // 格式：djicloudapi/任务id/annotated/图片名称
    const annotatedPath = `djicloudapi/${taskId}/annotated/${originalImageName}`;
    console.log(`构造标注图片路径: ${annotatedPath}`);

    let imageUrl: string | null = null;

    try {
      // 使用annotated路径获取图片URL
      imageUrl = await getImageUrl(annotatedPath);

      if (!imageUrl) {
        console.warn(`无法为标注图片${photoId}生成MinIO URL，路径: ${annotatedPath}`);
        return null;
      }

    } catch (urlError) {
      console.error(`生成标注图片URL失败:`, urlError);
      return null;
    }

    if (imageUrl) {
      const photoItem: MediaItem = {
        id: photoId,
        type: 'photo',
        url: imageUrl,
        name: photo.imageName || photo.image_name || `标注图片${index + 1}`,
        time: photo.captureTime || photo.capture_time || photo.createTime || photo.create_time || '',
        isFavorite: false,
        isHovered: false,
        isIssue: true, // 标记为问题图片
        // 保存原始数据供后续使用
        raw: {
          ...photo,
          annotatedPath: annotatedPath
        }
      };

      console.log(`成功创建标注图片媒体项: ${photoItem.name}`);
      return photoItem;
    } else {
      console.warn(`标注图片${photoId}没有有效的URL，跳过创建`);
    }

  } catch (err) {
    console.error(`处理标注图片${photo.id}时出错:`, err);
  }

  return null;
};

// 创建照片媒体项的工具函数 - 支持异步处理和更robust的URL生成
const createPhotoMediaItem = async (photo: any, index: number): Promise<MediaItem | null> => {
  try {
    const photoId = photo.id || `photo-${Date.now()}-${index}`;
    
    // 获取存储路径，支持多种字段名
    const storagePath = photo.storagePath || photo.cachePath || photo.storage_path || photo.cache_path;
    
    if (!storagePath) {
      console.warn(`照片${photoId}缺少存储路径`);
      return null;
    }
    
    // 规范化路径
    let normalizedPath = storagePath.trim();
    
    // 移除开头的斜杠
    if (normalizedPath.startsWith('/')) {
      normalizedPath = normalizedPath.substring(1);
    }
    
    // 处理Windows路径格式（如果存在）
    if (normalizedPath.match(/^[A-Z]:\\/i)) {
      // Windows路径转换为相对路径
      const pathParts = normalizedPath.split('\\');
      if (pathParts.length > 2) {
        normalizedPath = pathParts.slice(-2).join('/'); // 取最后两级目录
      }
    }
    
    let imageUrl: string | null = null;
    
    try {
      // 添加await以正确等待Promise解析
      imageUrl = await getImageUrl(normalizedPath);
      
      if (!imageUrl) {
        console.warn(`无法为照片${photoId}生成MinIO URL，路径: ${normalizedPath}`);
        return null; // 如果无法生成URL，直接返回null
      }
      
    } catch (urlError) {
      console.error(`生成照片URL失败:`, urlError);
      return null; // URL生成失败时返回null
    }
    
    // 检查标注状态 - 支持多种字段格式
    const isAnnotated = photo.isAnnotated === 1 || 
                       photo.isAnnotated === '1' || 
                       photo.isAnnotated === true || 
                       photo.is_annotated === 1 || 
                       photo.is_annotated === '1' || 
                       photo.is_annotated === true ||
                       photo.annotated === 1 ||
                       photo.annotated === '1' ||
                       photo.annotated === true;
    
    // 检查标注状态
    
    if (imageUrl) {
      const photoItem: MediaItem = {
        id: photoId,
        type: 'photo',
        url: imageUrl,
        name: photo.imageName || photo.image_name || `照片${index + 1}`,
        time: photo.captureTime || photo.capture_time || photo.createTime || photo.create_time || '', 
        isFavorite: false,
        isHovered: false,
        isIssue: isAnnotated,
        // 保存原始数据供后续使用
        raw: photo
      };
      
      // 照片媒体项创建成功
      
      return photoItem;
    } else {
      console.warn(`照片${photoId}没有有效的URL，跳过创建`);
    }
    
  } catch (err) {
    console.error(`处理照片${photo.id}时出错:`, err);
  }
  
  return null;
};

// 创建视频媒体项的工具函数
const createVideoMediaItem = async (video: any, index: number): Promise<MediaItem | null> => {
  try {
    const videoId = video.id || `video-${Date.now()}-${index}`;
    let videoUrl = '';

    if (video.videoUrl) {
      if (video.videoUrl.startsWith('http')) {
        videoUrl = video.videoUrl;
      } else {
        let normalizedPath = video.videoUrl.trim();
        if (normalizedPath.startsWith('/')) {
          normalizedPath = normalizedPath.substring(1);
        }
        // 添加await以正确等待Promise解析
        videoUrl = await getImageUrl(normalizedPath);
      }
    } else if (video.storagePath) {
      let normalizedPath = video.storagePath.trim();
      if (normalizedPath.startsWith('/')) {
        normalizedPath = normalizedPath.substring(1);
      }
      // 添加await以正确等待Promise解析
      videoUrl = await getImageUrl(normalizedPath);
    }

    if (videoUrl) {
      return {
        id: videoId,
        type: 'video',
        url: videoUrl,
        name: video.videoName || video.video_name || `视频${index + 1}`,
        time: video.captureTime || video.capture_time || video.createTime || video.create_time || '',
        isFavorite: false,
        isHovered: false,
        isIssue: false,
        // 保存原始数据供后续使用
        raw: video
      };
    }
  } catch (err) {
    console.error(`处理视频${video.id}时出错:`, err);
  }

  return null;
};

// 根据图片路径推导对应的视频路径
const deriveVideoPathFromImage = (imagePath: string, taskId: string): string[] => {
  try {
    if (!imagePath || !taskId) return [];

    // 从日志看到的实际路径格式：
    // 图片：1925481973718777858/5022365831485460481.jpeg
    // 视频：djicloudapi/1925481973718777858/DJI_20250616100939_0001_S.mp4

    const possibleVideoPaths: string[] = [];

    // 方案1：基于taskId构造标准DJI视频路径
    // 尝试不同的时间戳（当前时间前后几分钟）
    const now = new Date();
    const timeVariations: string[] = [];
    for (let i = -10; i <= 10; i++) {
      const time = new Date(now.getTime() + i * 60000); // 前后10分钟
      const timeStr = time.toISOString().replace(/[-:T]/g, '').slice(0, 14);
      timeVariations.push(timeStr);
    }

    // 生成可能的视频路径
    timeVariations.forEach(timeStr => {
      for (let seq = 1; seq <= 10; seq++) {
        const seqStr = seq.toString().padStart(4, '0');
        possibleVideoPaths.push(`djicloudapi/${taskId}/DJI_${timeStr}_${seqStr}_S.mp4`);
        possibleVideoPaths.push(`${taskId}/DJI_${timeStr}_${seqStr}_S.mp4`);
      }
    });

    // 方案2：基于已知的视频文件名
    // 从用户提供的信息：djicloudapi/1925481973718777858/DJI_20250616100939_0001_S.mp4
    possibleVideoPaths.push(`djicloudapi/${taskId}/DJI_20250616100939_0001_S.mp4`);
    possibleVideoPaths.push(`${taskId}/DJI_20250616100939_0001_S.mp4`);

    // 方案3：尝试其他常见的视频文件名模式
    const commonVideoNames = [
      'DJI_20250616100939_0001_S.mp4',
      'DJI_20250616100939_0002_S.mp4',
      'DJI_20250616100939_0003_S.mp4',
      'DJI_20250616100940_0001_S.mp4',
      'DJI_20250616100940_0002_S.mp4',
      'DJI_20250616100941_0001_S.mp4',
      'video.mp4',
      'output.mp4',
      'record.mp4'
    ];

    commonVideoNames.forEach(videoName => {
      possibleVideoPaths.push(`djicloudapi/${taskId}/${videoName}`);
      possibleVideoPaths.push(`${taskId}/${videoName}`);
    });

    // 去重
    return [...new Set(possibleVideoPaths)];
  } catch (error) {
    console.error('推导视频路径失败:', error);
    return [];
  }
};

// 根据图片数据创建对应的视频媒体项
const createVideoFromImageData = async (photo: any, index: number, taskId: string): Promise<MediaItem | null> => {
  try {
    // 获取图片的存储路径
    const imagePath = photo.storagePath || photo.cachePath || photo.storage_path || photo.cache_path;
    if (!imagePath) return null;

    // 推导可能的视频路径
    const possibleVideoPaths = deriveVideoPathFromImage(imagePath, taskId);
    if (possibleVideoPaths.length === 0) return null;

    // 尝试每个可能的视频路径，直到找到一个有效的
    let videoUrl: string | null = null;
    let successfulVideoPath: string | null = null;

    for (const videoPath of possibleVideoPaths) {
      try {
        console.log(`尝试视频路径: ${videoPath}`);
        videoUrl = await getImageUrl(videoPath);
        if (videoUrl) {
          successfulVideoPath = videoPath;
          console.log(`成功获取视频URL: ${videoPath} -> ${videoUrl}`);
          break;
        }
      } catch (error) {
        console.warn(`视频路径无效: ${videoPath}`, error);
        continue;
      }
    }

    if (!videoUrl || !successfulVideoPath) {
      console.warn(`所有推导的视频路径都无效，共尝试了 ${possibleVideoPaths.length} 个路径`);
      return null;
    }

    // 创建视频媒体项
    const videoId = `derived-video-${photo.id || Date.now()}-${index}`;
    const videoItem: MediaItem = {
      id: videoId,
      type: 'video',
      url: videoUrl,
      name: `视频_${photo.imageName || photo.image_name || `${index + 1}`}`,
      time: photo.captureTime || photo.capture_time || photo.createTime || photo.create_time || '',
      isFavorite: false,
      isHovered: false,
      isIssue: false,
      // 保存原始数据和推导信息
      raw: {
        ...photo,
        derivedFromImage: true,
        originalImagePath: imagePath,
        derivedVideoPath: successfulVideoPath,
        attemptedPaths: possibleVideoPaths
      }
    };

    return videoItem;
  } catch (error) {
    console.error('从图片数据创建视频项失败:', error);
    return null;
  }
};

// 从MinIO的annotated文件夹加载已标注图片
const loadAnnotatedImagesFromMinio = async (taskId: string, issuePhotoList: MediaItem[]) => {
  try {
    console.log('尝试从MinIO的annotated文件夹获取已标注图片，任务ID:', taskId);

    // 暂时简化实现，后续可以扩展为真正的MinIO文件列表查询
    // 这里可以根据实际的MinIO文件命名规则来调整
    console.log('MinIO annotated文件夹查询功能待实现，当前跳过');

  } catch (error) {
    console.error('从MinIO加载annotated图片失败:', error);
  }
};

// 加载被标记为问题的图片
const loadIssuePhotos = async () => {
  if (!props.record) {
    console.warn('记录数据不存在，无法加载问题图片');
    return;
  }

  const taskId = getValidTaskId();
  if (!taskId) {
    console.warn('无法获取taskId，跳过问题图片查询');
    return;
  }

  console.log('开始加载被标记为问题的图片，任务ID:', taskId);
  isLoadingMedia.value = true;

  try {
    // 确保MinIO配置已加载
    await fetchMinioConfig();

    // 查询该任务的所有图片，然后在前端过滤被标记的图片
    const photoResponse = await defHttp.get({
      url: '/flight/flightInspectionImages/list',
      params: {
        pageNo: 1,
        pageSize: 1000, // 获取所有图片
        taskId: taskId
        // 不传 isAnnotated 参数，避免后端枚举类型转换错误
      }
    });

    const allPhotoRecords = extractRecordsFromResponse(photoResponse);

    // 在前端过滤出被标记为问题的图片
    const photoRecords = allPhotoRecords.filter(photo => {
      // 检查多种可能的标注状态字段格式
      const isAnnotated = photo.isAnnotated === 1 ||
                         photo.isAnnotated === '1' ||
                         photo.isAnnotated === true ||
                         photo.is_annotated === 1 ||
                         photo.is_annotated === '1' ||
                         photo.is_annotated === true ||
                         photo.annotated === 1 ||
                         photo.annotated === '1' ||
                         photo.annotated === true;
      return isAnnotated;
    });

    console.log(`从 ${allPhotoRecords.length} 张图片中找到 ${photoRecords.length} 张被标记为问题的图片`);

    const issuePhotoList: MediaItem[] = [];

    // 如果数据库中没有找到被标记的图片，尝试从MinIO的annotated文件夹获取
    if (photoRecords.length === 0) {
      console.log('数据库中未找到被标记的图片，尝试从MinIO的annotated文件夹获取');
      await loadAnnotatedImagesFromMinio(taskId, issuePhotoList);
    }

    // 批量处理问题图片，使用annotated文件夹中的图片
    const batchSize = 10;
    for (let i = 0; i < photoRecords.length; i += batchSize) {
      const batch = photoRecords.slice(i, i + batchSize);
      const batchItems = await Promise.all(
        batch.map(async (photo: any, index: number) => {
          try {
            return await createAnnotatedPhotoMediaItem(photo, i + index, taskId);
          } catch (error) {
            console.error(`处理问题图片${photo.id}失败:`, error);
            return null;
          }
        })
      );

      // 添加成功处理的图片到列表
      batchItems.forEach(item => {
        if (item) {
          issuePhotoList.push(item);
        }
      });
    }

    // 更新媒体列表为只包含问题图片
    mediaList.value = issuePhotoList;
    console.log(`成功加载 ${issuePhotoList.length} 张问题图片`);

  } catch (error) {
    console.error('加载问题图片失败:', error);
  } finally {
    isLoadingMedia.value = false;
  }
};

// 统一的初始化函数
const initRecordData = async () => {
  if (!props.record) return;

  // 并行获取飞行任务详情和媒体列表
  await Promise.all([
    fetchFlightTaskData(),
    initMediaList()
  ]);
};

// 简化记录变化监听逻辑，主要依赖模态框打开事件
watch(() => props.record, (newRecord, oldRecord) => {
  // 只在记录发生变化且模态框已经打开时才重新初始化
  if (newRecord && newRecord.id !== oldRecord?.id && currentRecordId.value) {
    resetComponentState();
    initRecordData();
  }
}, { immediate: false });

// 处理媒体项悬停
const handleMediaHover = (id: string, isHovered: boolean) => {
  const item = mediaList.value.find(item => item.id === id);
  if (item) {
    item.isHovered = isHovered;
  }
};

// 处理收藏
const handleFavorite = (item: MediaItem) => {
  item.isFavorite = !item.isFavorite;
};

// 修改处理下载函数
const handleDownload = async (item: MediaItem) => {
  try {
    // 获取媒体文件
    const response = await fetch(item.url);
    const blob = await response.blob();
    
    if (showWatermark.value && props.record?.recordName) {
      // 如果开启水印，添加水印
      const orgName = extractOrgName(props.record.recordName);
      if (orgName) {
        if (item.type === 'photo') {
          // 为图片添加水印
          const watermarkedBlob = await addWatermarkToImage(blob, orgName);
          downloadFile(watermarkedBlob, `${item.name}.jpg`);
        } else {
          // 为视频添加水印
          const watermarkedBlob = await addWatermarkToVideo(blob, orgName);
          downloadFile(watermarkedBlob, `${item.name}.mp4`);
        }
      } else {
        // 无法提取组织名称，直接下载
        downloadFile(blob, `${item.name}.${item.type === 'photo' ? 'jpg' : 'mp4'}`);
      }
    } else {
      // 不添加水印，直接下载
      downloadFile(blob, `${item.name}.${item.type === 'photo' ? 'jpg' : 'mp4'}`);
    }
  } catch (error) {
    console.error('下载失败:', error);
    // 可以在这里添加错误提示
  }
};

// 添加下载文件的辅助函数
const downloadFile = (blob: Blob, filename: string) => {
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  
  // 触发下载
  document.body.appendChild(link);
  link.click();
  
  // 清理
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};

// 修改为图片添加水印的函数
const addWatermarkToImage = async (imageBlob: Blob, text: string): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const reader = new FileReader();
    
    reader.onload = (e) => {
      img.src = e.target?.result as string;
      
      img.onload = () => {
        // 创建画布
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        if (!ctx) {
          reject(new Error('无法创建画布上下文'));
          return;
        }
        
        // 设置画布尺寸
        canvas.width = img.width;
        canvas.height = img.height;
        
        // 绘制图片
        ctx.drawImage(img, 0, 0);
        
        // 设置水印样式
        ctx.font = '12px Arial';
        ctx.fillStyle = 'rgba(200, 200, 200, 0.7)';
        ctx.textAlign = 'right';
        ctx.textBaseline = 'bottom';
        
        // 绘制水印
        ctx.fillText(text, canvas.width - 10, canvas.height - 10);
        
        // 将画布转换为Blob
        canvas.toBlob((blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('无法创建Blob'));
          }
        }, 'image/jpeg', 0.9);
      };
      
      img.onerror = () => {
        reject(new Error('图片加载失败'));
      };
    };
    
    reader.onerror = () => {
      reject(new Error('文件读取失败'));
    };
    
    reader.readAsDataURL(imageBlob);
  });
};

// 视频水印处理函数（简化版本，建议后端处理）
const addWatermarkToVideo = async (videoBlob: Blob, text: string): Promise<Blob> => {
  // 视频水印处理复杂，建议使用后端服务
  // 这里返回原始视频
  console.warn('视频水印功能需要后端支持，当前返回原始视频');
  return videoBlob;
};

// 处理过滤类型变化
const handleFilterChange = (type: string) => {
  currentFilter.value = type;
};

// 处理媒体项点击
const handleMediaClick = (item: MediaItem) => {
  currentDetail.value = item;
  detailVisible.value = true;
  isZoomed.value = false;
  zoomLevel.value = 1;
};

// 处理图片点击
const handleImageClick = () => {
  isZoomed.value = !isZoomed.value;
};

// 处理鼠标滚轮
const handleWheel = (e: WheelEvent) => {
  if (currentDetail.value?.type === 'photo') {
    e.preventDefault();
    
    // 根据滚轮方向调整缩放级别
    if (e.deltaY < 0) {
      // 放大
      zoomLevel.value = Math.min(zoomLevel.value + 0.1, 3);
    } else {
      // 缩小
      zoomLevel.value = Math.max(zoomLevel.value - 0.1, 0.5);
    }
  }
};

// 关闭详情
const closeDetail = () => {
  currentDetail.value = null;
  detailVisible.value = false;
  isZoomed.value = false;
  zoomLevel.value = 1;
};

const closeModalAndEmit = () => {
  // 清理mapWork模块
  if (mapWork.onUnmounted) {
    try {
      mapWork.onUnmounted();
    } catch (e) {
      console.warn('清理mapWork模块时出错:', e);
    }
  }
  
  // 清理本地地图引用
  if (map.value) {
    map.value = null;
  }
  
  // 清理容器
  const mapContainer = document.getElementById('mars2dContainer');
  if (mapContainer) {
    mapContainer.innerHTML = '';
    mapContainer.style.cssText = '';
  }
  
  // 重置组件状态
  resetComponentState();
  
  closeModal();
};

// 组件变量定义 - 显式引用图标
const DownloadOutlined = DownloadIcon;
const StarOutlined = StarIcon;
const CloseOutlined = CloseIcon;

// 处理图片加载错误
const handleImageError = (event: Event, item: MediaItem) => {
  const imgElement = event.target as HTMLImageElement;
  if (imgElement) {
    imgElement.classList.remove('photo-image');
    imgElement.classList.add('error');
    imgElement.alt = '图片加载失败';
  }
};

// 处理图片加载成功
const handleImageLoad = (event: Event, item: MediaItem) => {
  const imgElement = event.target as HTMLImageElement;
  if (imgElement) {
    imgElement.classList.remove('error');
    imgElement.classList.add('photo-image');

    // 检查图片是否正常显示，如果有问题则自动修复
    setTimeout(() => {
      const rect = imgElement.getBoundingClientRect();
      const computedStyle = window.getComputedStyle(imgElement);

      if (rect.width === 0 || rect.height === 0 || computedStyle.opacity === '0' || computedStyle.visibility === 'hidden') {
        // 修复图片样式
        imgElement.style.cssText = `
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
          width: 100% !important;
          height: 100% !important;
          min-height: 100% !important;
          max-height: none !important;
          object-fit: cover !important;
          position: relative !important;
          z-index: 10 !important;
          box-sizing: border-box !important;
        `;

        // 修复父容器
        const mediaContent = imgElement.parentElement;
        const mediaItem = mediaContent?.parentElement;

        if (mediaContent) {
          mediaContent.style.cssText += `
            display: block !important;
            width: 100% !important;
            height: 100% !important;
            min-height: 100% !important;
            position: relative !important;
            overflow: hidden !important;
          `;
        }

        if (mediaItem) {
          mediaItem.style.cssText += `
            display: block !important;
            width: 100% !important;
            aspect-ratio: 4/3 !important;
            min-height: 0 !important;
            position: relative !important;
            overflow: hidden !important;
          `;
        }

        // 强制重排
        imgElement.offsetHeight;

        // 如果还是高度为0，使用绝对定位
        setTimeout(() => {
          if (imgElement.getBoundingClientRect().height === 0) {
            imgElement.style.cssText = `
              display: block !important;
              width: 100% !important;
              height: 81px !important;
              object-fit: cover !important;
              position: absolute !important;
              top: 0 !important;
              left: 0 !important;
              z-index: 10 !important;
            `;
          }
        }, 50);
      }
    }, 50);
  }
};

// 处理视频加载错误
const handleVideoError = (event: Event, item: MediaItem) => {
  const videoElement = event.target as HTMLVideoElement;
  if (videoElement) {
    console.error(`视频加载失败: ${item.name}`, item.url);
    videoElement.classList.add('error');

    // 可以在这里添加错误显示逻辑，比如显示一个错误图标
    const errorDiv = document.createElement('div');
    errorDiv.className = 'video-error';
    errorDiv.innerHTML = `
      <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; color: #999;">
        <div style="font-size: 24px; margin-bottom: 8px;">🎥</div>
        <div style="font-size: 12px;">视频加载失败</div>
      </div>
    `;

    const mediaContent = videoElement.parentElement;
    if (mediaContent) {
      mediaContent.appendChild(errorDiv);
      videoElement.style.display = 'none';
    }
  }
};

// 处理视频加载成功
const handleVideoLoad = (event: Event, item: MediaItem) => {
  const videoElement = event.target as HTMLVideoElement;
  if (videoElement) {
    console.log(`视频加载成功: ${item.name}`, item.url);
    videoElement.classList.remove('error');
    videoElement.classList.add('video-player');
  }
};

// 组件卸载时清理
onUnmounted(() => {
  // 清理mapWork模块
  if (mapWork.onUnmounted) {
    try {
      mapWork.onUnmounted();
    } catch (error) {
      console.error('清理mapWork模块失败:', error);
    }
  }
  
  // 清理本地地图引用
  if (map.value) {
    map.value = null;
  }
  
  // 重置状态
  resetComponentState();
});

// 优化的获取航线数据函数
const getFlightPathData = async () => {
  if (!props.record?.id) {
    return null;
  }

  try {
    let flightPath: any = null;
    
    // 查找有效的taskId字段
    let taskId = null;
    for (const field of CONFIG.API.TASK_ID_FIELDS) {
      if (props.record && props.record[field]) {
        taskId = props.record[field];
        break;
      }
    }
    
    // 通过task_id从flight_tasks表查询航线信息
    if (taskId) {
      const taskResponse = await defHttp.get({
        url: '/flight/flightTasks/queryById',
        params: { id: taskId },
        timeout: CONFIG.API.REQUEST_TIMEOUT
      });
      
      const taskData = taskResponse?.result || taskResponse;
      
      // 解析flight_path字段中的经纬度信息
      const flightPathField = CONFIG.API.FLIGHT_PATH_FIELDS.find(field => taskData?.[field]);
      if (flightPathField && taskData[flightPathField]) {
        try {
          flightPath = typeof taskData[flightPathField] === 'string' 
            ? JSON.parse(taskData[flightPathField]) 
            : taskData[flightPathField];
          
          // 验证航线数据格式
          if (!flightPath || !flightPath.coordinates || !Array.isArray(flightPath.coordinates)) {
            flightPath = null;
          }
          
        } catch (parseError) {
          flightPath = null;
        }
      }
    }
    
    // 备用方案：从记录本身获取
    if (!flightPath && props.record.flightPath) {
      try {
        flightPath = typeof props.record.flightPath === 'string' 
          ? JSON.parse(props.record.flightPath) 
          : props.record.flightPath;
      } catch (parseError) {
        // 解析失败时静默处理
      }
    }
    
    return flightPath;
    
  } catch (error) {
    // 返回默认航线数据以确保地图能正常显示
    return {
      type: "LineString",
      coordinates: [[25.0566, 102.8905], [25.0566, 102.8902]]
    };
  }
};

// 监听过滤器变化
watch([currentFilter], () => {
  // 过滤器状态变化时的处理
}, { immediate: true });

// 监听问题数复选框变化，重新查询被标记的图片
watch(showIssues, async (newValue, oldValue) => {
  // 避免初始化时的触发和重复触发
  if (newValue === oldValue || !props.record) return;

  if (newValue) {
    // 当勾选问题数时，查询被标记为问题的图片
    console.log('用户勾选问题数复选框，开始加载问题图片');
    await loadIssuePhotos();
  } else {
    // 当取消勾选时，恢复显示所有图片
    console.log('用户取消勾选问题数复选框，恢复显示所有图片');
    await initMediaList();
  }
}, { immediate: false });

// 监听媒体列表变化，确保视图正确更新
watch(mediaList, (newMediaList) => {
  if (newMediaList.length > 0 && !isLoadingMedia.value) {
    // 使用nextTick确保DOM更新
    nextTick(() => {
      // DOM渲染检查
      setTimeout(() => {
        document.querySelector('.media-grid');
      }, 100);
    });
  }
}, { immediate: false });

</script>

<style lang="less" scoped>
.record-modal {
  display: flex;
  height: 100%;
  gap: 20px;
  position: relative;
  overflow: hidden;
  padding: 0;
  margin: 0;

  .close-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    z-index: 1001;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 100px;
    cursor: pointer;
    transition: all 0.3s;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    &:hover {
      color: #1890ff;
      border-color: #1890ff;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
    }
  }

  .map-section {
    flex: 1;
    position: relative;
    min-width: 300px;
    max-width: 400px;
    overflow: hidden;
    padding: 0;
    margin: 0;

    .map-container {
      width: 100%;
      height: 100%;
      background: #f5f5f5;
      border-radius: 8px;
      overflow: hidden;
      position: relative;
      padding: 0;
      margin: 0;

      .record-map-view {
        width: 100%;
        height: 100%;
        background: #f5f5f5;
        border-radius: 8px;
        position: relative;
        z-index: 1;
        
        // 地图加载指示器
        &::before {
          content: '地图加载中...';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          color: #999;
          font-size: 14px;
          z-index: 2;
        }
        
        // Mars2D地图容器样式 
        :deep(#mars2dContainer) {
          width: 100%;
          height: 100%;
          border-radius: 8px;
          overflow: hidden;
          position: relative;
          z-index: 1;
        }
        
        // 地图容器内部样式
        :deep(.mars2d-container) {
          border-radius: 8px;
          overflow: hidden;
          position: relative;
          z-index: 1;
        }
        
        // Mars2D地图控件样式调整
        :deep(.cesium-viewer) {
          border-radius: 8px;
          position: relative;
          z-index: 1;
        }
        
        :deep(.cesium-widget) {
          border-radius: 8px;
          position: relative;
          z-index: 1;
        }
        
        // 确保Mars2D的控件不会覆盖我们的UI
        :deep(.leaflet-control-container) {
          z-index: 2 !important;
        }
        
        :deep(.leaflet-popup) {
          z-index: 999 !important;
        }
      }

      .flight-info {
        position: absolute;
        bottom: 16px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        justify-content: space-around;
        width: 90%;
        padding: 12px;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        pointer-events: none;
        flex-wrap: wrap;
        gap: 8px;

        .info-item {
          text-align: center;
          flex: 1;
          min-width: 80px;

          .value {
            font-size: 16px;
            font-weight: 500;
            color: #1890ff;
            margin-bottom: 2px;
          }

          .label {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.45);
          }
        }
      }

      .map-error {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        z-index: 10;
        background: rgba(255, 255, 255, 0.9);
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

        .error-icon {
          font-size: 32px;
          margin-bottom: 8px;
        }

        .error-text {
          color: #ff4d4f;
          margin-bottom: 12px;
          font-size: 14px;
        }
      }
    }
  }

  .content-section {
    flex: 0.8;
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 0;
    margin: 0;

    .record-header {
      .record-title {
        font-size: 14px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        margin-top: 8px;
        margin-bottom: 4px;
      }

      .record-info {
        .info-row {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 12px;

          .label {
            color: rgba(0, 0, 0, 0.45);
          }

          .value {
            color: rgba(0, 0, 0, 0.45);
          }

          .separator {
            color: rgba(0, 0, 0, 0.25);
            margin: 0 4px;
          }
        }
      }
    }

    .filter-section {
      display: flex;
      align-items: center;
      gap: 12px;

      .filter-buttons {
        display: flex;
        gap: 6px;

        :deep(.ant-btn) {
          font-size: 12px;
          padding: 0 8px;
          height: 26px;
          border-radius: 15px;
        }
      }

      .filter-options {
        display: flex;
        align-items: center;
        gap: 19px;
        margin-left: 12px;

        :deep(.ant-checkbox-wrapper) {
          font-size: 12px;
        }
      }
    }

    .content-display {
      flex: 1;
      overflow-y: auto;
      padding: 12px;

      .media-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 10px;
        padding: 10px;
        width: 100%;
        height: auto;
        overflow: visible;

                  .media-item {
            position: relative !important;
            border-radius: 6px;
            overflow: hidden;
            transition: all 0.3s ease;
            aspect-ratio: 4/3 !important;
            background: #f5f5f5;
            cursor: pointer;
            display: block !important;
            width: 100% !important;
            min-height: 0 !important;

          &:hover {
            border: 1px solid #1890ff;
          }

                      .media-content {
              position: relative !important;
              width: 100% !important;
              height: 100% !important;
              display: block !important;
              min-height: 100% !important;

            img {
              width: 100% !important;
              height: 100% !important;
              min-height: 100% !important;
              max-height: none !important;
              object-fit: cover !important;
              border-radius: 6px;
              display: block !important;
              background: #f5f5f5;
              box-sizing: border-box !important;
              
              &.photo-image {
                background: #f5f5f5;
              }

              &.video-player {
                background: #000;
                object-fit: cover;
              }

              &.error {
                opacity: 0.5;
                background: #f5f5f5 url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ccc"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM5 19l3.5-4.5 2.5 3.01L14.5 12l4.5 7H5z"/></svg>') no-repeat center;
                background-size: 32px 32px;
              }
            }

            video {
              width: 100%;
              height: 100%;
              object-fit: cover;
              border-radius: 6px;
            }

            .media-info {
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;
              padding: 3px;
              display: flex;
              justify-content: space-between;
              align-items: center;
              background: rgba(0, 0, 0, 0.5);
              transition: all 0.3s ease;

              .time {
                color: #fff;
                font-size: 10px;
              }

              .favorite-btn {
                color: #fff;
                
                .is-favorite {
                  color: #faad14;
                }
              }
            }

            .hover-actions {
              position: absolute;
              top: 4px;
              right: 4px;
              background: rgba(24, 144, 255, 0.8);
              padding: 1px;
              border-radius: 2px;

              .download-btn {
                color: #fff;
                border: none;
                background: transparent;
                font-size: 9px;
                padding: 0;
                height: 18px;
                width: 18px;
                line-height: 18px;
                display: flex;
                align-items: center;
                justify-content: center;
                
                :deep(.anticon) {
                  font-size: 9px;
                }
                
                &:hover {
                  background: rgba(255, 255, 255, 0.1);
                }
              }
            }

            .video-error {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              background-color: rgba(0, 0, 0, 0.1);
              border-radius: 4px;
              z-index: 5;
            }
          }
        }
      }
    }
  }
}

// 加载状态样式
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  
  .loading-content {
    text-align: center;
    padding: 20px;
    
    .loading-icon {
      font-size: 32px;
      margin-bottom: 16px;
    }
    
    .loading-text {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      margin-top: 8px;
    }
  }
  
  :deep(.ant-spin-text) {
    color: #1890ff;
  }
}

:deep(.custom-modal) {
  .ant-modal {
    top: -50px !important;
    margin: 0 !important;
    padding: 0;
    border: none;
    position: absolute !important;
  }

  .ant-modal-content {
    overflow: hidden;
    border: none;
    padding: 0;
  }

  .ant-modal-body {
    overflow: hidden;
    padding: 0;
  }
}

.detail-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;
  overflow: hidden;
  
  .detail-close {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    cursor: pointer;
    color: #fff;
    font-size: 16px;
    z-index: 10;
    transition: all 0.3s;
    
    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
  
  .detail-content {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &.zoomed {
      .detail-image {
        cursor: zoom-out;
      }
    }
    
    .detail-image {
      width: 100%;
      height: 100%;
      object-fit: contain;
      cursor: zoom-in;
      transition: transform 0.1s;
      transform-origin: center center;
    }
    
    .detail-video {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
}

// 修改问题图标样式
.issue-icon {
  position: absolute;
  top: 0px;
  left: 2px;
  width: 15px;
  height: 15px;
  z-index: 2;
  
  img {
    width: 100%;
    height: 100%;
  }
}

// 空状态样式
.empty-info {
  grid-column: 1 / -1;
  padding: 40px 20px;
  text-align: center;
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  .empty-title {
    font-size: 16px;
    color: rgba(0, 0, 0, 0.65);
    margin-bottom: 12px;
    font-weight: 500;
  }
  
  .empty-desc {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.45);
    margin-bottom: 20px;
    line-height: 1.6;
  }
  
  .empty-tips {
    text-align: left;
    background: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    border-left: 3px solid #1890ff;
    
    span {
      font-weight: 500;
      color: #1890ff;
      margin-bottom: 8px;
      display: block;
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.65);
        margin-bottom: 4px;
        line-height: 1.4;
      }
    }
  }
}

</style>
