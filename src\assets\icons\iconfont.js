// 图标字体配置
const iconfont = {
  'icon-biaoji': `<svg class="icon" width="200px" height="200.00px" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z" fill="currentColor"/>
    <path d="M512 336c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176z m0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z" fill="currentColor"/>
  </svg>`,
  'icon-fenxiang': `<svg class="icon" width="200px" height="200.00px" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z" fill="currentColor"/>
    <path d="M512 336c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176z m0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z" fill="currentColor"/>
  </svg>`,
  'icon-weixin': `<svg class="icon" width="200px" height="200.00px" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <path d="M692.3 347.8c-11.3-11.3-29.6-11.3-40.9 0L512 574.2 372.6 434.8c-11.3-11.3-29.6-11.3-40.9 0-11.3 11.3-11.3 29.6 0 40.9l139.4 139.4c11.3 11.3 29.6 11.3 40.9 0l139.4-139.4c11.3-11.3 11.3-29.6 0-40.9z" fill="currentColor"/>
  </svg>`,
  'icon-QQ': `<svg class="icon" width="200px" height="200.00px" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z" fill="currentColor"/>
  </svg>`,
  'icon-youjian': `<svg class="icon" width="200px" height="200.00px" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <path d="M811.5 867.8h-680c-28.4 0-51.5-23.1-51.5-51.5V250.9c0-28.4 23.1-51.5 51.5-51.5h680c28.4 0 51.5 23.1 51.5 51.5v565.4c0 28.4-23.1 51.5-51.5 51.5z m-669-62.6h658V262h-658v543.2z" fill="currentColor"/>
    <path d="M433.1 553.4L252.6 414.2c-13.7-10.5-16.2-30.2-5.7-43.8 10.5-13.7 30.2-16.2 43.9-5.7l153.9 118.7c4.4 3.4 10.6 3.4 15 0l155.1-118.9c13.7-10.5 33.3-7.9 43.8 5.8s7.9 33.3-5.8 43.8L471.2 553.5c-11.3 8.6-26.9 8.6-38.1-0.1z" fill="currentColor"/>
    <path d="M584.3 583.4h118.2v41.7H584.3zM584.3 667.4h118.2v41.7H584.3z" fill="currentColor"/>
  </svg>`,
  'icon-xiazai': `<svg class="icon" width="200px" height="200.00px" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z" fill="currentColor"/>
    <path d="M512 336c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176z m0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z" fill="currentColor"/>
  </svg>`,
};

// 创建SVG sprite
const createSvgSprite = () => {
  const sprite = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
  sprite.setAttribute('style', 'position: absolute; width: 0; height: 0;');

  const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');

  Object.entries(iconfont).forEach(([id, svg]) => {
    const symbol = document.createElementNS('http://www.w3.org/2000/svg', 'symbol');
    symbol.setAttribute('id', id);
    symbol.setAttribute('viewBox', '0 0 1024 1024');
    symbol.innerHTML = svg;
    defs.appendChild(symbol);
  });

  sprite.appendChild(defs);
  document.body.appendChild(sprite);
};

// 初始化
createSvgSprite();

export default iconfont;
