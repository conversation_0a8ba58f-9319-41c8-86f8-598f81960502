import { defHttp } from '/@/utils/http/axios';
import { ReportTemplateModel } from './model/reportTemplateModel';

enum Api {
  // 获取模板列表
  LIST = '/flight/reportTemplateText/list',
  // 保存模板
  SAVE = '/flight/reportTemplateText/save',
  // 激活模板
  ACTIVATE = '/flight/reportTemplateText/activate',
  // 删除模板
  DELETE = '/flight/reportTemplateText/delete',
  // 获取激活模板
  ACTIVE = '/flight/reportTemplateText/active',
}

/**
 * 获取模板列表
 * @param params 查询参数
 */
export function getTemplateList(params: {
  pageNo: number;
  pageSize: number;
  type?: string;
  name?: string;
}) {
  return defHttp.get({ url: Api.LIST, params });
}

/**
 * 保存或更新模板
 * @param data 模板数据
 * @param activate 是否同时激活该模板
 */
export function saveTemplate(data: ReportTemplateModel, activate: boolean = false) {
  return defHttp.post({ url: Api.SAVE, params: { activate }, data });
}

/**
 * 删除模板
 * @param id 模板ID
 */
export function deleteTemplate(id: number) {
  return defHttp.delete({ 
    url: Api.DELETE, 
    params: { id } 
  }, {
    joinParamsToUrl: true // 强制将参数添加到URL中
  });
}

/**
 * 激活模板
 * @param id 模板ID
 */
export function activateTemplate(id: number) {
  return defHttp.post({ 
    url: Api.ACTIVATE, 
    params: { id },
    data: {}
  }, {
    joinParamsToUrl: true // 强制将参数添加到URL中
  });
}

/**
 * 获取当前激活的模板
 * @param type 模板类型
 */
export function getActiveTemplate(type: string) {
  return defHttp.get({ url: Api.ACTIVE, params: { type } });
} 