<template>
  <div class="flight-record">
    <div class="p-4">
      <!-- 搜索框 -->
      <div class="search-filter">
        <div class="search-wrapper">
          <a-auto-complete 
            v-model:value="searchValue" 
            placeholder="请输入记录名称进行搜索" 
            style="width: 220px" 
            allowClear 
            class="custom-search-input"
            @change="handleSearch"
          >
            <template #prefix>
              <SearchOutlinedIcon style="color: rgba(0, 0, 0, 0.45);" />
            </template>
          </a-auto-complete>
        </div>
      </div>

      <!-- 记录列表标题栏 -->
      <div class="record-header">
        <div class="record-title">
          飞行记录名称
          <a-dropdown>
            <a-button type="link" class="filter-btn">
              <FilterOutlinedIcon />
            </a-button>
            <template #overlay>
              <a-menu @click="handleTypeFilter" :selectedKeys="[selectedType]">
                <a-menu-item key="all">全部类型</a-menu-item>
                <a-menu-item key="photo">拍照</a-menu-item>
                <a-menu-item key="video">视频</a-menu-item>
                <a-menu-item key="panorama">全景</a-menu-item>
                <a-menu-item key="3d">三维</a-menu-item>
                <a-menu-item key="ortho">正射</a-menu-item>
                <a-menu-item key="full">全覆盖</a-menu-item>
                <a-menu-item key="auto">一键飞行</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
        <div class="record-airport">机场名称</div>

        <div class="record-creator">创建人</div>
        <div class="record-time">创建时间</div>
        <div class="record-photos">照片数</div>
        <div class="record-issues">问题照片数</div>
        <div class="record-status">照片状态</div>
        <div class="record-action">操作</div>
      </div>

      <!-- 记录列表 -->
      <div class="record-list">
        <div v-if="recordList.length === 0" class="empty-list">
          <a-empty description="暂无数据" />
        </div>
        <div v-else>
          <div class="record-item" v-for="(item, index) in recordList" :key="index">
            <div class="record-content">
              <div class="record-info">
                <div class="record-details">
                  <div class="record-name" :title="item.recordName">{{ item.recordName }}</div>
                  <div class="record-type">{{ item.type }}</div>
                </div>
              </div>
              <div class="record-airport" :title="item.airport">{{ item.airport }}</div>
              <div class="record-creator" :title="item.creator">{{ item.creator }}</div>
              <div class="record-time" :title="item.flightTime">{{ item.flightTime }}</div>
              <div class="record-photos">{{ item.photoNums }}</div>
              <div class="record-issues">{{ item.issuePhotoNums }}</div>
              <div class="record-status">
                <a-tag :class="getStatusClass(item.rawPhotoStatus)">{{ item.photoStatus }}</a-tag>
              </div>
              <div class="record-action">
                <a-space>
                  <a-button type="link" class="action-btn" @click="handleViewRecord(item)">查看</a-button>
                </a-space>
              </div>
            </div>
          </div>
        </div>
        <!-- 分页组件 -->
        <div class="pagination-wrapper" v-if="total > 0">
          <a-pagination
            v-model:current="currentPage"
            :total="total"
            :pageSize="pageSize"
            :showSizeChanger="false"
            :showQuickJumper="false"
            :showTotal="(total) => `共有 ${total} 条数据`"
            @change="handlePageChange"
            class="custom-pagination"
          />
        </div>
      </div>
    </div>

    <!-- 记录详情弹窗 -->
    <RecordModal 
      @register="register" 
      :minHeight="300" 
      :record="currentRecord"
    />
  </div>
</template>

<script lang="ts" name="flight-record-table" setup>
  import { ref, onMounted, computed, defineAsyncComponent, nextTick, watch } from 'vue';    
  import { useModal } from '/@/components/Modal';    
  import { defHttp } from '/@/utils/http/axios';
  import { useGo } from '/@/hooks/web/usePage';
  import { message } from 'ant-design-vue';
  import dayjs from "dayjs"
  import { SearchOutlined, FilterOutlined } from '@ant-design/icons-vue';

  // 确保图标组件被使用
  const SearchOutlinedIcon = SearchOutlined;
  const FilterOutlinedIcon = FilterOutlined;

  // 异步导入组件，添加错误处理
  const RecordModal = defineAsyncComponent({
    loader: () => import('./RecordModal.vue'),
    errorComponent: { template: '<div>模态框组件加载失败</div>' },
    loadingComponent: { template: '<div>正在加载...</div>' },
    delay: 200,
    timeout: 3000
  });

  // 使用标准方式注册
  const [register, modalMethods] = useModal();
  
  // 添加模态框就绪状态
  const modalReady = ref(false);
  
  // 组件预加载状态
  const componentPreloaded = ref(false);
  
  // 预加载模态框组件
  const preloadModalComponent = async () => {
    try {
      // 预加载组件
      await import('./RecordModal.vue');
      componentPreloaded.value = true;
      console.log('模态框组件预加载完成');
    } catch (error) {
      console.error('模态框组件预加载失败:', error);
    }
  };
  
  // 安全的openModal函数
  const openModal = (visible: boolean, data?: any) => {
    if (modalMethods && modalMethods.openModal) {
      modalMethods.openModal(visible, data);
    } else {
      console.error('Modal methods not ready');
      message.error('模态框未准备就绪，请稍后重试');
    }
  };
  
  // 使用页面跳转 hook
  const go = useGo();

  // 定义记录类型接口
  interface Record {
    id: string;
    recordName: string;
    type: string;
    airport: string;
    airportName?: string;
    creator: string;
    createBy?: string;
    flightTime: string;
    takeoffTime?: string;
    landingTime?: string;
    createTime?: string;
    photoNums: number;
    issuePhotoNums: number;
    photoStatus: string;
    rawPhotoStatus: number;  // 原始照片状态值
    flightPath?: string | any;  // 飞行航线数据
    taskId?: string;           // 关联的任务ID
    flightTaskId?: string;     // 飞行任务ID
    flightDistance?: number;   // 飞行距离
    flightDuration?: string;   // 飞行时长
    sysOrgCode?: string;       // 组织代码
    updateTime?: string;       // 更新时间
    status?: number | string;  // 记录状态
  }

  // 在script标签开始后添加配置常量
  const CONFIG = {
    // 分页配置
    PAGINATION: {
      PAGE_SIZE: 10,
      DEFAULT_PAGE: 1
    },
    
    // 任务类型映射
    TASK_TYPES: {
      0: '拍照',
      1: '视频', 
      2: '全景',
      3: '三维',
      4: '正射',
      5: '全覆盖',
      6: '一键飞行'
    },
    
    // 照片状态映射
    PHOTO_STATUS: {
      0: '未同步',
      1: '同步完成',
      2: '同步失败'
    },
    
    // 过滤器映射
    FILTERS: {
      TYPE: {
        'photo': '拍照',
        'video': '视频',
        'panorama': '全景',
        '3d': '三维',
        'ortho': '正射',
        'full': '全覆盖',
        'auto': '一键飞行'
      },
      AIRPORT: {
        'airport1': '滇中展业发展集团机场1',
        'airport2': '滇中展业发展集团机场2'
      }
    },
    
    // 时间字段优先级
    TIME_FIELDS: ['createTime', 'updateTime', 'createDate', 'flightTime', 'takeoffTime', 'landingTime']
  };

  // 工具函数
  const utils = {
    // 创建反向映射
    createReverseMap: (map: { [key: string]: any }) => {
      const reverseMap: { [key: string]: any } = {};
      Object.entries(map).forEach(([key, value]) => {
        reverseMap[value] = parseInt(key);
      });
      return reverseMap;
    },
    
    // 格式化时间
    formatTime: (time: string | Date | null): string => {
      if (!time) return '未知时间';
      try {
        const parsedTime = dayjs(time);
        return parsedTime.isValid() ? parsedTime.format('YYYY-MM-DD HH:mm:ss') : '未知时间';
      } catch (e) {
        return '未知时间';
      }
    },
    
    // 处理枚举值
    processEnumValue: (value: any, defaultValue: number = 0): number => {
      if (value === undefined || value === null) return defaultValue;
      
      if (typeof value === 'object' && value.value !== undefined) {
        return value.value;
      }
      
      if (typeof value === 'number') {
        return value;
      }
      
      if (typeof value === 'string') {
        const numValue = parseInt(value);
        return isNaN(numValue) ? defaultValue : numValue;
      }
      
      return defaultValue;
    },
    
    // 获取最佳时间字段
    getBestTimeField: (item: any): string => {
      for (const field of CONFIG.TIME_FIELDS) {
        if (item[field]) {
          try {
            const time = dayjs(item[field]);
            if (time.isValid()) {
              return time.format('YYYY-MM-DD HH:mm:ss');
            }
          } catch (e) {
            continue;
          }
        }
      }
      return '未知时间';
    }
  };

  // 反向映射
  const taskTypeReverseMap = utils.createReverseMap(CONFIG.TASK_TYPES);

  // 响应式数据
  const searchValue = ref('');
  const selectedType = ref('all');
  const selectedAirport = ref('all');
  const allRecordList = ref<Record[]>([]);
  const currentPage = ref(CONFIG.PAGINATION.DEFAULT_PAGE);
  const pageSize = ref(CONFIG.PAGINATION.PAGE_SIZE);
  const total = ref(0);
  const currentRecord = ref<Record | null>(null);
  const loading = ref(false);
  const sortOrder = ref<'asc' | 'desc' | null>(null);

  // 获取记录列表
  const getRecordList = async () => {
    try {
      loading.value = true;
      const params = buildRequestParams();
      
      const res = await defHttp.get({ 
        url: '/flight/flightRecords/list', 
        params 
      });

      if (res && res.records) {
        const formattedRecords = res.records.map(formatRecord);
        
        // 按创建时间倒序排序
        formattedRecords.sort((a, b) => {
          const timeA = dayjs(a.createTime);
          const timeB = dayjs(b.createTime);
          if (!timeA.isValid()) return 1;
          if (!timeB.isValid()) return -1;
          return timeB.valueOf() - timeA.valueOf();
        });

        allRecordList.value = formattedRecords;
        total.value = res.total || 0;
      } else {
        allRecordList.value = [];
        total.value = 0;
      }
    } catch (error) {
      console.error('获取记录列表失败：', error);
      allRecordList.value = [];
      total.value = 0;
    } finally {
      loading.value = false;
    }
  };

  // 构建请求参数
  const buildRequestParams = () => {
    const params: any = {
      pageNo: currentPage.value,
      pageSize: pageSize.value,
      column: 'createTime',
      order: 'desc',
      recordName: searchValue.value.trim() || undefined,

    };
    
    // 添加类型筛选
    if (selectedType.value !== 'all') {
      params.type = CONFIG.FILTERS.TYPE[selectedType.value];
    }
    
    // 添加机场筛选
    if (selectedAirport.value !== 'all') {
      params.airport = CONFIG.FILTERS.AIRPORT[selectedAirport.value];
    }
    
    return params;
  };

  // 格式化单条记录
  const formatRecord = (item: any): Record => {
    // 处理时间字段
    const formattedTime = utils.getBestTimeField(item);
    
    // 处理任务类型
    const type = utils.processEnumValue(item.type, 0);
    
    // 处理照片状态
    let photoStatus = utils.processEnumValue(item.photoStatus, 0);
    
         // 如果是字符串状态，转换为数值
     if (typeof item.photoStatus === 'string') {
       const statusMap: { [key: string]: number } = {
         '未同步': 0,
         '同步完成': 1,
         '同步失败': 2
       };
       photoStatus = statusMap[item.photoStatus] ?? 0;
     }

    return {
      id: item.id || '',
      recordName: item.recordName || '未命名记录',
      type: CONFIG.TASK_TYPES[type] || '未知类型',
      airport: item.airportName || item.airport || '未知机场',
      creator: item.createBy || '未知',
      flightTime: formattedTime,
      photoNums: item.photoNums || 0,
      issuePhotoNums: item.issuePhotoNums || 0,
      photoStatus: CONFIG.PHOTO_STATUS[photoStatus] || '未知状态',
      rawPhotoStatus: photoStatus,
      flightPath: item.flightPath,
      taskId: item.taskId,
      flightTaskId: item.flightTaskId,
      flightDistance: item.flightDistance,
      flightDuration: item.flightDuration,
      sysOrgCode: item.sysOrgCode,
      updateTime: item.updateTime,
      createTime: item.createTime,
      takeoffTime: item.takeoffTime,
      landingTime: item.landingTime,
      status: item.status
    };
  };

  // 监听搜索值变化
  watch(searchValue, (newVal) => {
    if (!newVal) {
      currentPage.value = CONFIG.PAGINATION.DEFAULT_PAGE;
      getRecordList();
    }
  });

  // 处理搜索
  const handleSearch = () => {
    currentPage.value = CONFIG.PAGINATION.DEFAULT_PAGE;
    getRecordList();
  };

  // 处理分页变化
  const handlePageChange = (page: number) => {
    currentPage.value = page;
    getRecordList();
  };

  // 过滤后的记录列表
  const recordList = computed(() => allRecordList.value);

  // 状态样式
  const getStatusClass = (status: number): string => {
    const statusNum = typeof status === 'number' ? status : 0;
    const classMap: { [key: number]: string } = {
      0: 'status-pending',
      1: 'status-approved', 
      2: 'status-rejected'
    };
    return classMap[statusNum] || 'status-pending';
  };

  // 任务类型筛选处理
  const handleTypeFilter = ({ key }: { key: string }) => {
    selectedType.value = key;
    currentPage.value = CONFIG.PAGINATION.DEFAULT_PAGE;
    getRecordList();
  };

  // 机场筛选处理
  const handleAirportFilter = ({ key }: { key: string }) => {
    selectedAirport.value = key;
    currentPage.value = CONFIG.PAGINATION.DEFAULT_PAGE;
    getRecordList();
  };

  // 处理查看记录
  async function handleViewRecord(record: Record) {
    try {
      if (!record || !record.id) {
        message.error('记录数据无效，无法查看详情');
        return;
      }
      
      console.log('📱 准备打开模态框，检查组件状态...');
      
      // 确保组件已预加载
      if (!componentPreloaded.value) {
        console.log('⏳ 组件未预加载，开始预加载...');
        await preloadModalComponent();
        // 给组件一点时间完全注册
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      currentRecord.value = record;
      await nextTick();
      
      // 等待一个额外的渲染周期，确保组件完全准备好
      await new Promise(resolve => setTimeout(resolve, 50));
      
      console.log('打开模态框...');
      
      // 尝试打开模态框
      openModal(true, { record });
      
    } catch (error) {
      console.error('查看记录详情出错:', error);
      message.error('查看详情失败，请稍后重试');
    }
  }

  // 排序切换
  const handleSort = async () => {
    if (sortOrder.value === null) {
      sortOrder.value = 'asc';
    } else if (sortOrder.value === 'asc') {
      sortOrder.value = 'desc';
    } else {
      sortOrder.value = null;
    }
    currentPage.value = CONFIG.PAGINATION.DEFAULT_PAGE;
    getRecordList();
  };

  onMounted(() => {
    getRecordList();
    // 预加载模态框组件，避免第一次使用时的延迟
    preloadModalComponent();
  });
</script>

<style lang="less" scoped>
.flight-record {
  background: #fff;
  height: 100vh;
  display: flex;
  flex-direction: column;

  .p-4 {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 100%;
    padding-bottom: 0;
  }

  .search-filter {
    margin-bottom: 13px;
    padding-left: 0;
    position: relative;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    gap: 12px;

    .search-wrapper {
      position: relative;
      width: 220px;
      :deep(.custom-search-input) {
        .ant-select-selector {
          border-radius: 50px !important;
          border: 1px solid #9c9b9b !important;
          transition: all 0.3s;
          overflow: hidden;
          background: #fff;
          height: 32px !important;
          padding: 4px 11px !important;

          .ant-select-selection-search-input {
            height: 24px !important;
            line-height: 24px !important;
            margin-top: 2px !important;
          }

          .ant-select-selection-placeholder {
            line-height: 24px !important;
            margin-top: 0px !important;
          }

          .ant-select-selection-item {
            line-height: 24px !important;
            margin-top: 1px !important;
          }
        }
        .ant-select-dropdown {
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          .ant-select-item {
            padding: 8px 12px;
            font-size: 14px;
            transition: all 0.3s;
            &:hover {
              background-color: #f5f5f5;
            }
            &-option-selected {
              background-color: #e6f7ff;
              color: #1890ff;
            }
          }
        }
        .ant-input-suffix {
          margin-left: 0;
          padding-left: 4px;
          border-left: 1px solid #d9d9d9;
          .search-btn {
            height: 22px;
            padding: 0 4px;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
            border: none;
            background: transparent;
            transition: all 0.3s;
            &:hover {
              color: #1890ff;
            }
            &:disabled {
              color: rgba(0, 0, 0, 0.25);
              cursor: not-allowed;
            }
          }
        }
      }
    }
  }

  .record-header {
    display: flex;
    align-items: center;
    padding: 8px 0;
    margin-bottom: 12px;
    flex-shrink: 0;

    .record-title {
      flex: 2.8;
      color: rgba(0, 0, 0, 0.45);
      padding-left: 15px;
      display: flex;
      align-items: center;
      gap: 2px;

      .filter-btn {
        padding: 0;
        height: 24px;
        color: rgba(0, 0, 0, 0.45);
        display: flex;
        align-items: center;
        
        &:hover {
          color: #1890ff;
        }
      }
    }

    .record-airport {
      flex: 0.9;
      text-align: left;
      color: rgba(0, 0, 0, 0.45);
      padding-left: 65px;
      display: flex;
      align-items: center;
      gap: 4px;

      .filter-btn {
        padding: 0;
        height: 24px;
        color: rgba(0, 0, 0, 0.45);
        display: flex;
        align-items: center;
        
        &:hover {
          color: #1890ff;
        }
      }
    }

    .record-creator {
      flex: 1.2;
      text-align: center;
      color: rgba(0, 0, 0, 0.45);
      padding-left: 85px;
    }

    .record-time {
      flex: 1.0;
      text-align: left;
      color: rgba(0, 0, 0, 0.45);
      padding-left: 65px;
      display: flex;
      align-items: center;
      gap: 4px;

      .sort-icon {
        display: inline-flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.45);
        transition: all 0.3s;
        margin-left: 2px;
        cursor: pointer;

        .anticon {
          font-size: 14px;
          transition: all 0.3s;
          transform: scale(0.9);

          &.active {
            color: #1890ff;
            transform: scale(1);
          }

          &.inactive {
            opacity: 0.5;
          }
        }

        &:hover {
          .anticon.inactive {
            opacity: 1;
            color: #1890ff;
            transform: scale(1);
          }
        }
      }
    }

    .record-photos {
      flex: 1.6;
      text-align: center;
      color: rgba(0, 0, 0, 0.45);
      padding-left: 35px;
    }

    .record-issues {
      flex: 0.9;
      text-align: center;
      color: rgba(0, 0, 0, 0.45);
      padding-left: 4px;
    }

    .record-status {
      flex: 2.2;
      text-align: center;

      :deep(.ant-tag) {
        padding: 2px 12px;
        font-size: 12px;
        border-radius: 12px;
        
        &.status-pending {
          background: #fff1f0;
          border-color: #fff1f0;
          color: #f5222d;
        }
        
        &.status-approved {
          background: #f6ffed;
          border-color: #f6ffed;
          color: #52c41a;
        }
        
        &.status-rejected {
          background: #fff1f0;
          border-color: #fff1f0;
          color: #f5222d;
        }
      }
    }

    .record-action {
      flex: 1.1;
      text-align: left;
      color: rgba(0, 0, 0, 0.45);
      padding-left: 0;
    }
  }

  .record-list {
    flex: 1;
    overflow-y: auto;
    padding-right: 0px;
    padding-bottom: 210px;
    height: calc(100vh - 100px);
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    position: relative;
    min-height: 0;
    display: flex;
    flex-direction: column;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
      border-radius: 3px;
      transition: all 0.3s ease;

      &:hover {
        background-color: #bfbfbf;
      }
    }

    &::-webkit-scrollbar-track {
      background-color: #f0f0f0;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-corner {
      background-color: #f0f0f0;
    }

    .empty-list {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }

    .record-item {
      margin-bottom: 12px;
      transition: all 0.3s ease;
      flex-shrink: 0;

      &:last-child {
        margin-bottom: 0;
        padding-bottom: 16px;
      }

      &:hover {
        transform: translateY(-2px);
      }

      .record-content {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        padding: 16px;
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
        min-height: 60px;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
        }

        .record-info {
          flex: 2;
          display: flex;
          align-items: center;
          gap: 12px;

          .record-details {
            flex: 1;
            min-width: 0;

            .record-name {
              font-size: 14px;
              color: rgba(0, 0, 0, 0.88);
              margin-bottom: 4px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              max-width: 220px;
              position: relative;
            }

            .record-type {
              font-size: 12px;
              color: rgba(0, 0, 0, 0.45);
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              padding-left: 0;
            }
          }
        }

        .record-airport {
          flex: 1;
          text-align: left;
          color: rgba(0, 0, 0, 0.88);
          font-size: 14px;
          white-space: nowrap;
          overflow: visible;
          text-overflow: clip;
          padding-left: 10px;
        }

        .record-creator {
          flex: 1;
          text-align: center;
          color: rgba(0, 0, 0, 0.88);
          font-size: 14px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-left: 35px;
          padding-left: 0px;
        }

        .record-time {
          flex: 1.5;
          text-align: left;
          color: rgba(0, 0, 0, 0.88);
          font-size: 14px;
          white-space: nowrap;
          overflow: visible;
          text-overflow: clip;
          padding-left: 28px;
        }

        .record-photos {
          flex: 0.6;
          text-align: center;
          color: rgba(0, 0, 0, 0.88);
          font-size: 14px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          padding-left: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0;
          padding: 0;
        }

        .record-issues {
          flex: 1;
          text-align: center;
          color: rgba(0, 0, 0, 0.88);
          font-size: 14px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          padding-left: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0;
          padding: 0;
        }

        .record-status {
          flex: 1.3;
          text-align: center;
          color: rgba(0, 0, 0, 0.88);
          display: flex;
          align-items: center;
          justify-content: center;
          padding-left: 18px;
          margin: 0;
          padding: 0;

          :deep(.ant-tag) {
            padding: 2px 12px;
            font-size: 12px;
            border-radius: 12px;
            margin: 0;
            
            &.status-pending {
              background: #fff1f0;
              border-color: #fff1f0;
              color: #f5222d;
            }
            
            &.status-approved {
              background: #f6ffed;
              border-color: #f6ffed;
              color: #52c41a;
            }
            
            &.status-rejected {
              background: #fff1f0;
              border-color: #fff1f0;
              color: #f5222d;
            }
          }
        }

        .record-action {
          flex: 0.6;
          text-align: left;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          margin: 0;
          padding: 0;

          :deep(.ant-space) {
            display: flex;
            justify-content: flex-start;
            gap: 8px;
            margin: 0;
          }

          :deep(.action-btn) {
            height: 24px;
            padding: 0 8px;
            font-size: 14px;
            border-radius: 4px;
            display: inline-flex;
            align-items: center;
            justify-content: center;

            &.ant-btn-link {
              color: #1890ff;
              background: transparent;
              border: none;

              &:hover {
                color: #40a9ff;
              }
            }
          }
        }
      }
    }
  }

  .pagination-wrapper {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 12px 0;
    background: #fff;
    border-top: 1px solid #f0f0f0;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
    .custom-pagination {
      :deep(.ant-pagination-item) {
        min-width: 28px;
        height: 28px;
        line-height: 26px;
        border-radius: 20px;
        transition: all 0.3s;
        font-size: 13px;
        &:hover {
          border-color: #1890ff;
        }
        &-active {
          background: #1890ff;
          border-color: #1890ff;
          a {
            color: #fff;
          }
          &:hover {
            background: #40a9ff;
            border-color: #40a9ff;
          }
        }
      }
      :deep(.ant-pagination-prev),
      :deep(.ant-pagination-next) {
        min-width: 28px;
        height: 28px;
        line-height: 26px;
        .ant-pagination-item-link {
          border-radius: 20px;
          transition: all 0.3s;
          font-size: 13px;
          &:hover {
            border-color: #1890ff;
            color: #1890ff;
          }
        }
      }
      :deep(.ant-pagination-jump-prev),
      :deep(.ant-pagination-jump-next) {
        min-width: 28px;
        height: 28px;
        line-height: 26px;
        .ant-pagination-item-container {
          .ant-pagination-item-ellipsis {
            color: rgba(0, 0, 0, 0.45);
            font-size: 13px;
          }
        }
      }
      :deep(.ant-pagination-total-text) {
        margin-right: 16px;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
      }
    }
  }
}
</style>
