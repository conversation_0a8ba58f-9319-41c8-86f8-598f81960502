export interface BasicPageParams {
  page: number;
  pageSize: number;
}

export interface BasicFetchResult<T extends any> {
  items: T[];
  total: number;
}

export interface BasicResult<T extends any> {
  records: T[];
  total: number;
}

/**
 * 基础数据模型
 * 提供所有模型通用属性
 */
export interface BaseModel {
  /**
   * 主键ID
   */
  id?: number | string;
  
  /**
   * 创建时间
   */
  createTime?: string;
  
  /**
   * 更新时间
   */
  updateTime?: string;
}
