<template>
  <div class="minio-auto-sync-manage">
    <PageWrapper dense contentFullHeight fixedHeight contentClass="flex">
      <div class="p-4 bg-white">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center">
            <Icon icon="ant-design:sync-outlined" class="mr-2 text-xl" />
            <span class="text-lg font-medium">MinIO自动同步管理</span>
          </div>
          <div class="flex items-center space-x-2">
            <a-button type="primary" @click="handleRefreshStatus" :loading="statusLoading">
              <Icon icon="ant-design:reload-outlined" class="mr-1" />
              刷新状态
            </a-button>
          </div>
        </div>

        <!-- 同步状态卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <a-card size="small" class="text-center">
            <div class="text-2xl font-bold text-blue-600">{{ syncStatus.schedulerEnabled ? '已启用' : '已禁用' }}</div>
            <div class="text-gray-500 mt-1">定时同步状态</div>
          </a-card>
          <a-card size="small" class="text-center">
            <div class="text-2xl font-bold text-green-600">{{ syncStatus.nextSyncTime || '--' }}</div>
            <div class="text-gray-500 mt-1">下次同步时间</div>
          </a-card>
          <a-card size="small" class="text-center">
            <div class="text-2xl font-bold text-orange-600">{{ syncStatus.syncMode || '--' }}</div>
            <div class="text-gray-500 mt-1">同步模式</div>
          </a-card>
          <a-card size="small" class="text-center">
            <div class="text-2xl font-bold text-purple-600">{{ imageStats.imageCount || 0 }}</div>
            <div class="text-gray-500 mt-1">MinIO图片总数</div>
          </a-card>
        </div>

        <!-- 操作按钮区域 -->
        <div class="bg-gray-50 p-4 rounded-lg mb-6">
          <div class="flex flex-wrap gap-3">
            <a-button 
              type="primary" 
              size="large"
              @click="handleFullSync" 
              :loading="fullSyncLoading"
              class="flex-1 min-w-[200px]"
            >
              <Icon icon="ant-design:cloud-sync-outlined" class="mr-2" />
              执行全量同步
            </a-button>
            
            <a-button 
              size="large"
              @click="showTaskSyncModal = true"
              class="flex-1 min-w-[200px]"
            >
              <Icon icon="ant-design:file-sync-outlined" class="mr-2" />
              指定任务同步
            </a-button>
            
            <a-button 
              size="large"
              @click="handleGetImageStats"
              :loading="statsLoading"
              class="flex-1 min-w-[200px]"
            >
              <Icon icon="ant-design:bar-chart-outlined" class="mr-2" />
              获取图片统计
            </a-button>
          </div>
        </div>

        <!-- 同步结果展示 -->
        <div v-if="lastSyncResult" class="mb-6">
          <a-card title="最近同步结果" size="small">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div class="text-center">
                <div class="text-xl font-bold text-green-600">{{ lastSyncResult.inserted || 0 }}</div>
                <div class="text-gray-500">新增图片</div>
              </div>
              <div class="text-center">
                <div class="text-xl font-bold text-blue-600">{{ lastSyncResult.updated || 0 }}</div>
                <div class="text-gray-500">更新图片</div>
              </div>
              <div class="text-center">
                <div class="text-xl font-bold text-orange-600">{{ lastSyncResult.skipped || 0 }}</div>
                <div class="text-gray-500">跳过文件</div>
              </div>
              <div class="text-center">
                <div class="text-xl font-bold text-red-600">{{ lastSyncResult.errors || 0 }}</div>
                <div class="text-gray-500">错误文件</div>
              </div>
            </div>
            <div class="mt-4 text-center text-gray-600">
              检测到任务数: {{ lastSyncResult.detectedTasks || 0 }} 个
            </div>
          </a-card>
        </div>

        <!-- 说明信息 -->
        <a-card title="功能说明" size="small">
          <div class="space-y-2 text-gray-600">
            <p><strong>全量同步：</strong>{{ syncStatus.description || '自动识别MinIO中所有任务的图片并同步到数据库，跳过无法识别taskId的文件' }}</p>
            <p><strong>指定任务同步：</strong>只同步指定任务ID的图片文件</p>
            <p><strong>定时同步：</strong>系统每天14:50自动执行全量同步</p>
            <p><strong>同步策略：</strong>同步失败的任务会被跳过，不影响其他任务的同步</p>
          </div>
        </a-card>
      </div>
    </PageWrapper>

    <!-- 指定任务同步弹窗 -->
    <a-modal
      v-model:visible="showTaskSyncModal"
      title="指定任务同步"
      @ok="handleTaskSync"
      :confirmLoading="taskSyncLoading"
      width="500px"
    >
      <div class="py-4">
        <a-form-item label="任务ID" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-input
            v-model:value="taskSyncForm.taskId"
            placeholder="请输入要同步的任务ID（纯数字）"
            @pressEnter="handleTaskSync"
          />
        </a-form-item>
        <div class="text-gray-500 text-sm mt-2">
          <Icon icon="ant-design:info-circle-outlined" class="mr-1" />
          任务ID必须是纯数字格式，例如：1234567890
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { PageWrapper } from '/@/components/Page';
import { Icon } from '/@/components/Icon';
import {
  triggerFullSync as apiTriggerFullSync,
  triggerTaskSync as apiTriggerTaskSync,
  getImageStats as apiGetImageStats,
  getSyncStatus as apiGetSyncStatus,
  type SyncResult,
  type SyncStatus,
  type ImageStats
} from './MinioAutoSync.api';

// 响应式数据
const statusLoading = ref(false);
const fullSyncLoading = ref(false);
const taskSyncLoading = ref(false);
const statsLoading = ref(false);
const showTaskSyncModal = ref(false);

// 同步状态
const syncStatus = reactive<SyncStatus>({
  schedulerEnabled: true,
  lastSyncTime: '',
  nextSyncTime: '每天14:50',
  syncMode: 'SKIP_UNKNOWN',
  description: ''
});

// 图片统计
const imageStats = reactive<ImageStats>({
  imageCount: 0,
  taskId: undefined
});

// 最近同步结果
const lastSyncResult = ref<SyncResult | null>(null);

// 任务同步表单
const taskSyncForm = reactive({
  taskId: ''
});

// 获取同步状态
const getSyncStatus = async () => {
  try {
    statusLoading.value = true;
    const result = await apiGetSyncStatus();
    if (result.success) {
      Object.assign(syncStatus, result.result);
    }
  } catch (error) {
    console.error('获取同步状态失败:', error);
  } finally {
    statusLoading.value = false;
  }
};

// 获取图片统计
const getImageStats = async (taskId?: string) => {
  try {
    const result = await apiGetImageStats(taskId);
    if (result.success) {
      Object.assign(imageStats, result.result);
    }
    return result;
  } catch (error) {
    console.error('获取图片统计失败:', error);
    throw error;
  }
};

// 处理刷新状态
const handleRefreshStatus = async () => {
  await Promise.all([
    getSyncStatus(),
    getImageStats()
  ]);
  message.success('状态刷新成功');
};

// 处理全量同步
const handleFullSync = async () => {
  try {
    fullSyncLoading.value = true;
    message.loading('正在执行全量同步，请稍候...', 0);

    const result = await apiTriggerFullSync();

    message.destroy();

    if (result.success) {
      lastSyncResult.value = result.result;
      message.success(result.message || '全量同步完成');
      // 刷新统计数据
      await getImageStats();
    } else {
      message.error(result.message || '全量同步失败');
    }
  } catch (error: any) {
    message.destroy();
    message.error('全量同步异常: ' + (error.message || '未知错误'));
  } finally {
    fullSyncLoading.value = false;
  }
};

// 处理任务同步
const handleTaskSync = async () => {
  if (!taskSyncForm.taskId.trim()) {
    message.error('请输入任务ID');
    return;
  }

  if (!/^\d+$/.test(taskSyncForm.taskId.trim())) {
    message.error('任务ID必须是纯数字格式');
    return;
  }

  try {
    taskSyncLoading.value = true;
    message.loading('正在同步指定任务，请稍候...', 0);

    const result = await apiTriggerTaskSync(taskSyncForm.taskId.trim());

    message.destroy();

    if (result.success) {
      lastSyncResult.value = result.result;
      message.success(result.message || '任务同步完成');
      showTaskSyncModal.value = false;
      taskSyncForm.taskId = '';
      // 刷新统计数据
      await getImageStats();
    } else {
      message.error(result.message || '任务同步失败');
    }
  } catch (error: any) {
    message.destroy();
    message.error('任务同步异常: ' + (error.message || '未知错误'));
  } finally {
    taskSyncLoading.value = false;
  }
};

// 处理获取图片统计
const handleGetImageStats = async () => {
  try {
    statsLoading.value = true;
    const result = await getImageStats();
    if (result.success) {
      message.success(result.message || '获取统计成功');
    }
  } catch (error: any) {
    message.error('获取统计失败: ' + (error.message || '未知错误'));
  } finally {
    statsLoading.value = false;
  }
};

// 组件挂载时初始化
onMounted(() => {
  handleRefreshStatus();
});
</script>

<style lang="less" scoped>
.minio-auto-sync-manage {
  height: 100%;
  
  .ant-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .grid {
    display: grid;
  }
  
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  
  @media (min-width: 768px) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
    
    .md\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  
  @media (min-width: 1024px) {
    .lg\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
}
</style>
