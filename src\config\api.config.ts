/**
 * API路径配置文件
 * 用于统一管理API路径，方便在不同环境中切换
 */

// API基础路径前缀 
export const API_BASE_PREFIX = '';  // 不需要前缀

// 数据检测模块API
export const INSPECTION_API = {
  // 热力图数据
  GET_HEATMAP: '/data/inspection/getHeatmapData',
  
  // 获取所有热力图数据
  GET_ALL_HEATMAP: '/data/inspection/getHeatmapData',
  
  // 获取热力图颜色配置
  GET_HEATMAP_COLOR: '/data/inspection/getHeatmapColorConfig',
  
  // 获取初始中心点
  GET_INITIAL_CENTER: '/data/inspection/getInitialCenter',
  
  // 图片查询
  QUERY_IMAGES: '/data/inspection/queryImages',
  
  // 从数据库获取图片
  GET_IMAGE_FROM_DB: '/data/inspection/getImageFromDb',
  
  // 从数据库获取缩略图
  GET_THUMBNAIL_FROM_DB: '/data/inspection/getThumbnailFromDb',
  
  // 获取图片的Base64编码
  GET_IMAGE_BASE64: '/data/inspection/getImageBase64',
  
  // 获取完整图片数据（带前缀的Base64）
  GET_IMAGE_DATA: '/data/inspection/getImageData',
  
  // 图片上传
  UPLOAD_IMAGE: '/data/inspection/upload',
  
  // 批量删除
  DELETE_IMAGES: '/data/inspection/batchDelete',
  
  // 图片标记相关
  ADD_IMAGE_MARK: '/data/inspection/addImageMark',
  GET_IMAGE_MARKS: '/data/inspection/getImageMarks',
  DELETE_IMAGE_MARK: '/data/inspection/deleteImageMark',
  
  // 分享
  SHARE_IMAGE: '/data/inspection/shareImage',
  
  GET_IMAGES: '/inspection/getImages',
  GET_HEATMAP_DATA: '/inspection/getHeatmapData',
  GET_COLOR_GRADIENT: '/inspection/getColorGradient',

};

// 添加辅助函数，根据环境切换API路径
export const getApiPath = (path: string, useJeecgbootPrefix = false) => {
  return useJeecgbootPrefix ? `${path}` : path;
};

// 导出默认配置
export default {
  API_BASE_PREFIX,
  INSPECTION_API,
  getApiPath
}; 