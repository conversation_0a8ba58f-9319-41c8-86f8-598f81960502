// 高德地图配置
export const MAP_CONFIG = {
  key: 'b1ac2c0be5e1b806f817e48df92e35da', // 请替换为实际的高德地图 API key
  version: '2.0',
  plugins: ['AMap.HeatMap', 'AMap.Satellite', 'AMap.TileLayer'],
  zoom: 13,
  defaultCenter: {
    lng: 116.397428,
    lat: 39.90923
  },
  retryTimes: 3,
  retryDelay: 2000, // 重试延迟时间（毫秒）
  timeout: 10000,    // 请求超时时间（毫秒）
};

// 热力图配置
export const HEATMAP_CONFIG = {
  radius: 25,
  opacity: [0, 0.8],
  gradient: {
    
      '1-5': '#ADD8E6',    // 淡蓝色
      '6-10': '#98FB98',   // 浅绿色
      '11-15': '#32CD32',  // 绿色
      '16-20': '#FFA500',  // 橙色
      '21-25': '#FF6347',  // 番茄红
      '26+': '#B22222'     // 深红色
  },
  zIndex: 1,
  renderOnZooming: false
};

// 地图样式配置
export const MAP_STYLE_CONFIG = {
  layers: [
    {
      name: 'satellite',
      type: 'Satellite',
      visible: true,
      zIndex: 0,
      opacity: 1
    },
    {
      name: 'roadNet',
      type: 'RoadNet',
      visible: true,
      zIndex: 1,
      opacity: 0.7
    }
  ],
  viewMode: '3D',
  pitch: 0,
  skyColor: '#1a1b1f'
};

interface HeatPointVO {
  longitude: number;  // 经度
  latitude: number;   // 纬度
  count: number;      // 热力值
  color: string;      // 颜色
}