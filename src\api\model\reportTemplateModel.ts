import { BaseModel } from './baseModel';

/**
 * 报告模板数据模型
 */
export interface ReportTemplateModel extends BaseModel {
  /**
   * 模板名称
   */
  name: string;
  
  /**
   * 模板类型：daily-日报，weekly-周报，monthly-月报
   */
  type: 'daily' | 'weekly' | 'monthly';
  
  /**
   * 是否激活
   */
  isActive: boolean;
  
  /**
   * 报告描述
   */
  description?: string;
  
  /**
   * 总体情况
   */
  summary?: string;
  
  /**
   * 等效价值
   */
  equivalentValue?: string;
  
  /**
   * 飞行汇总
   */
  flightSummary?: string;
  
  /**
   * 成果汇总
   */
  resultSummary?: string;
} 