import { defHttp } from '/@/utils/http/axios';

enum Api {
  // 自动同步管理接口
  triggerFullSync = '/data/minioAutoSync/triggerFullSync',
  triggerTaskSync = '/data/minioAutoSync/triggerTaskSync',
  getImageStats = '/data/minioAutoSync/getImageStats',
  getSyncStatus = '/data/minioAutoSync/getSyncStatus',
}

/**
 * 触发全量自动同步
 */
export const triggerFullSync = () => {
  return defHttp.post({
    url: Api.triggerFullSync,
    timeout: 300000, // 5分钟超时
  });
};

/**
 * 触发指定任务同步
 * @param taskId 任务ID
 */
export const triggerTaskSync = (taskId: string) => {
  return defHttp.post({
    url: Api.triggerTaskSync,
    params: { taskId },
    timeout: 300000, // 5分钟超时
  });
};

/**
 * 获取MinIO图片统计
 * @param taskId 任务ID（可选）
 */
export const getImageStats = (taskId?: string) => {
  const params = taskId ? { taskId } : {};
  return defHttp.get({
    url: Api.getImageStats,
    params,
  });
};

/**
 * 获取同步状态
 */
export const getSyncStatus = () => {
  return defHttp.get({
    url: Api.getSyncStatus,
  });
};

/**
 * 同步结果接口定义
 */
export interface SyncResult {
  inserted: number;
  updated: number;
  skipped: number;
  errors: number;
  total: number;
  detectedTasks: number;
  elapsedSeconds?: number;
}

/**
 * 同步状态接口定义
 */
export interface SyncStatus {
  schedulerEnabled: boolean;
  lastSyncTime: string;
  nextSyncTime: string;
  syncMode: string;
  description: string;
}

/**
 * 图片统计接口定义
 */
export interface ImageStats {
  imageCount: number;
  taskId?: string;
}
