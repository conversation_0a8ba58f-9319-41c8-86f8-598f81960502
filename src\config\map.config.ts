// 高德地图配置
export const MAP_CONFIG = {
  // 官方申请地址：https://lbs.amap.com/dev/key/app
  key: 'b1ac2c0be5e1b806f817e48df92e35da', // 此KEY若失效，需要替换为有效的KEY
  version: '2.0',
  plugins: [
    'AMap.Scale',
    'AMap.ToolBar',
    'AMap.Geolocation',
    'AMap.Weather',
    'AMap.TileLayer.Satellite',
    'AMap.TileLayer.RoadNet',
    'AMap.Geocoder',
    'AMap.MoveAnimation'
  ],
  
  // 地图样式配置
  style: {
    dark: 'amap://styles/dark',
    normal: 'amap://styles/normal',
    satellite: 'amap://styles/satellite'
  },
  
  // 默认地图中心点（北京）
  defaultCenter: {
    lng: 116.397428,
    lat: 39.90923
  },
  
  // 地图视图配置
  view: {
    zoom: 13,
    minZoom: 3,
    maxZoom: 20,
    pitch: 45,
    rotation: 0
  },
  
  // 无人机图标配置
  droneMarker: {
    size: 32,
    image: '/img/drone-icon.png',
    circleRadius: 50,
    circleColor: '#1890ff',
    circleOpacity: 0.2,
    strokeColor: '#1890ff',
    strokeOpacity: 0.8,
    strokeWeight: 2
  },
  
  // 刷新间隔（毫秒）
  updateInterval: 5000,
  
  // 静态地图配置
  staticMap: {
    width: 800,
    height: 600,
    zoom: 13
  }
};

// 热力图配置
export const HEATMAP_CONFIG = {
  radius: 25,
  opacity: [0, 0.8],
  gradient: {
    0.2: '#B19CD9',  // 1-3次：淡紫色
    0.3: '#90EE90',  // 3-5次：浅绿色
    0.4: '#228B22',  // 5-8次：绿色
    0.6: '#FFFACD',  // 8-10次：淡黄色
    0.8: '#FFD700',  // 10-12次：黄色
    1.0: '#FF0000'   // 12次以上：红色
  },
  zIndex: 1,
  renderOnZooming: false
};

// 地图样式配置
export const MAP_STYLE_CONFIG = {
  zoom: 13,
  resizeEnable: true,
  viewMode: '2D',
  zooms: [3, 20],
  showBuildingBlock: true,
  mapStyle: 'amap://styles/normal',
  features: ['bg', 'road', 'building']
};